import React from "react";
import { Input } from "./input";

// Temporary replacement for CurrencyInput to fix contextMap error
// This provides basic number input functionality without react-number-format
export interface CurrencyInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  thousandSeparator?: boolean;
  currency?: string;
  onValueChange?: (values: { floatValue?: number; value: string }) => void;
  min?: number;
  max?: number;
}

export function CurrencyInput({
  thousandSeparator = true,
  currency,
  onValueChange,
  onChange,
  value,
  ...props
}: CurrencyInputProps) {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value;
    const floatValue = parseFloat(inputValue) || 0;

    // Call the onValueChange callback if provided
    if (onValueChange) {
      onValueChange({
        floatValue: isNaN(floatValue) ? undefined : floatValue,
        value: inputValue,
      });
    }

    // Call the original onChange if provided
    if (onChange) {
      onChange(e);
    }
  };

  return (
    <Input
      type="number"
      step="0.01"
      value={value}
      onChange={handleChange}
      {...props}
    />
  );
}
