#!/usr/bin/env bun

import { connectDb } from "./src/db";

async function fixBankStatus() {
  console.log("🔧 Adding missing status column to bank_connections...");
  
  const db = await connectDb();
  
  try {
    // Add status column to bank_connections if it doesn't exist
    await db.execute(`
      ALTER TABLE bank_connections 
      ADD COLUMN IF NOT EXISTS status TEXT DEFAULT 'connected';
    `);
    
    console.log("✅ Added status column to bank_connections");
    
    // Check the current structure
    const result = await db.execute(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'bank_connections' 
      AND column_name = 'status'
      ORDER BY ordinal_position;
    `);
    
    console.log("📋 Status column details:");
    console.table(result.rows);
    
    console.log("🎉 Bank connections status column fixed successfully!");
    
  } catch (error) {
    console.error("❌ Error fixing bank status:", error);
    throw error;
  }
}

fixBankStatus().catch(console.error);
