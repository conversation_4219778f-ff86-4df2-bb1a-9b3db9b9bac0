#!/usr/bin/env bun
import { connectDb } from "./src/db/index.ts";
import { users, usersOnTeam, teams } from "./src/db/schema.ts";
import { eq } from "drizzle-orm";

async function createDevUser() {
  const db = await connectDb();
  
  console.log('👤 Creating development user...');
  
  const teamId = '86338a52-5196-401a-a7fc-d0ee8635e078';
  const userId = 'dev-user-1234567890';
  const email = '<EMAIL>';
  
  try {
    // First, check if team exists
    const existingTeam = await db
      .select()
      .from(teams)
      .where(eq(teams.id, teamId))
      .limit(1);
      
    console.log('Existing team:', existingTeam.length > 0 ? 'Found' : 'Not found');
    
    // If no team exists, create one
    if (existingTeam.length === 0) {
      await db.insert(teams).values({
        id: teamId,
        name: 'Development Team',
        plan: 'pro',
      });
      console.log('✅ Created development team');
    }
    
    // 1. Create/update user in users table
    await db.insert(users).values({
      id: userId,
      email: email,
      fullName: 'Development User',
      teamId: teamId,
    }).onConflictDoUpdate({
      target: users.id,
      set: {
        email: email,
        fullName: 'Development User',
        teamId: teamId,
      }
    });
    
    // 2. Add user to team with owner role
    await db.insert(usersOnTeam).values({
      userId: userId,
      teamId: teamId,
      role: 'owner',
    }).onConflictDoUpdate({
      target: [usersOnTeam.userId, usersOnTeam.teamId],
      set: {
        role: 'owner',
      }
    });
    
    console.log('✅ Development user created:');
    console.log('   ID:', userId);
    console.log('   Email:', email);
    console.log('   Team ID:', teamId);
    console.log('   Role: owner');
    
  } catch (error) {
    console.error('❌ Error creating user:', error.message);
    console.error(error);
  }
}

createDevUser().catch(console.error);
