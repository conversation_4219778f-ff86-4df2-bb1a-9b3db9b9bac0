import postgres from "postgres";

const connectionString = process.env.DATABASE_SESSION_POOLER!;
const sql = postgres(connectionString);

async function fixBankTables() {
  console.log("🔧 Fixing bank tables structure...");
  
  try {
    // Check current bank_connections structure
    const bankConnectionsInfo = await sql`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'bank_connections' 
      ORDER BY ordinal_position
    `;
    console.log("📋 Current bank_connections structure:");
    console.table(bankConnectionsInfo);

    // Add missing columns to bank_connections
    await sql`
      ALTER TABLE bank_connections 
      ADD COLUMN IF NOT EXISTS "logo_url" text,
      ADD COLUMN IF NOT EXISTS "expires_at" timestamp with time zone,
      ADD COLUMN IF NOT EXISTS "enrollment_id" text,
      ADD COLUMN IF NOT EXISTS "institution_id" text,
      ADD COLUMN IF NOT EXISTS "reference_id" text,
      ADD COLUMN IF NOT EXISTS "last_accessed" timestamp with time zone,
      ADD COLUMN IF NOT EXISTS "access_token" text
    `;
    console.log("✅ Added missing columns to bank_connections");

    // Check current bank_accounts structure
    const bankAccountsInfo = await sql`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'bank_accounts' 
      ORDER BY ordinal_position
    `;
    console.log("📋 Current bank_accounts structure:");
    console.table(bankAccountsInfo);

    // Add missing columns to bank_accounts
    await sql`
      ALTER TABLE bank_accounts 
      ADD COLUMN IF NOT EXISTS "enabled" boolean DEFAULT true,
      ADD COLUMN IF NOT EXISTS "manual" boolean DEFAULT false,
      ADD COLUMN IF NOT EXISTS "balance" numeric(10, 2) DEFAULT 0,
      ADD COLUMN IF NOT EXISTS "error_retries" integer DEFAULT 0
    `;
    console.log("✅ Added missing columns to bank_accounts");

    console.log("🎉 Bank tables fixed successfully!");
    
  } catch (error) {
    console.error("❌ Error fixing bank tables:", error);
  } finally {
    await sql.end();
  }
}

fixBankTables();
