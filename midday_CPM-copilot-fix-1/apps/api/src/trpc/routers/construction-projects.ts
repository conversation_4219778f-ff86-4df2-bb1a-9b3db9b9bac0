import {
  createAnnotation,
  createProgressUpdate,
  deleteConstructionProject,
  getConstructionProjectById,
  getConstructionProjects,
  getProjectAnnotations,
  getProjectProgressUpdates,
  upsertConstructionProject,
} from "@api/db/queries/construction-projects";
import {
  createAnnotationSchema,
  createProgressUpdateSchema,
  deleteConstructionProjectSchema,
  getConstructionProjectByIdSchema,
  getConstructionProjectsSchema,
  upsertConstructionProjectSchema,
} from "@api/schemas/construction-projects";
import { createTRPCRouter, protectedProcedure } from "@api/trpc/init";
import { z } from "zod";

export const constructionProjectsRouter = createTRPCRouter({
  get: protectedProcedure
    .input(getConstructionProjectsSchema.optional())
    .query(async ({ input, ctx: { db, teamId } }) => {
      return getConstructionProjects(db, {
        ...input,
        teamId: teamId!,
      });
    }),

  upsert: protectedProcedure
    .input(upsertConstructionProjectSchema)
    .mutation(async ({ input, ctx: { db, teamId, user } }) => {
      return upsertConstructionProject(db, {
        ...input,
        teamId: teamId!,
        userId: user.id,
      });
    }),

  delete: protectedProcedure
    .input(deleteConstructionProjectSchema)
    .mutation(async ({ input, ctx: { db, teamId } }) => {
      return deleteConstructionProject(db, {
        ...input,
        teamId: teamId!,
      });
    }),

  getById: protectedProcedure
    .input(getConstructionProjectByIdSchema)
    .query(async ({ input, ctx: { db, teamId } }) => {
      return getConstructionProjectById(db, {
        ...input,
        teamId: teamId!,
      });
    }),

  createProgressUpdate: protectedProcedure
    .input(createProgressUpdateSchema)
    .mutation(async ({ input, ctx: { db, teamId, user } }) => {
      return createProgressUpdate(db, {
        ...input,
        teamId: teamId!,
        userId: user.id,
      });
    }),

  getProgressUpdates: protectedProcedure
    .input(
      z.object({
        projectId: z.string().uuid(),
        limit: z.number().min(1).max(100).optional().default(50),
      })
    )
    .query(async ({ input, ctx: { db, teamId } }) => {
      return getProjectProgressUpdates(db, {
        ...input,
        teamId: teamId!,
      });
    }),

  createAnnotation: protectedProcedure
    .input(createAnnotationSchema)
    .mutation(async ({ input, ctx: { db, teamId, user } }) => {
      return createAnnotation(db, {
        ...input,
        teamId: teamId!,
        userId: user.id,
      });
    }),

  getAnnotations: protectedProcedure
    .input(
      z.object({
        projectId: z.string().uuid(),
        status: z.string().optional().default("active"),
      })
    )
    .query(async ({ input, ctx: { db, teamId } }) => {
      return getProjectAnnotations(db, {
        ...input,
        teamId: teamId!,
      });
    }),

  // Additional helper endpoints for construction-specific features
  updateProjectStatus: protectedProcedure
    .input(
      z.object({
        id: z.string().uuid(),
        status: z.enum(["planning", "in_progress", "on_hold", "completed", "cancelled"]),
      })
    )
    .mutation(async ({ input, ctx: { db, teamId, user } }) => {
      return upsertConstructionProject(db, {
        id: input.id,
        teamId: teamId!,
        userId: user.id,
        name: "", // This will be ignored in update
        status: input.status,
      });
    }),

  updateCompletionPercentage: protectedProcedure
    .input(
      z.object({
        id: z.string().uuid(),
        completionPercentage: z.number().min(0).max(100),
      })
    )
    .mutation(async ({ input, ctx: { db, teamId, user } }) => {
      return upsertConstructionProject(db, {
        id: input.id,
        teamId: teamId!,
        userId: user.id,
        name: "", // This will be ignored in update
        completionPercentage: input.completionPercentage,
      });
    }),

  updateCurrentPhase: protectedProcedure
    .input(
      z.object({
        id: z.string().uuid(),
        currentPhase: z.enum([
          "site_preparation",
          "foundation",
          "framing",
          "roofing",
          "electrical",
          "plumbing",
          "insulation",
          "drywall",
          "flooring",
          "painting",
          "final_inspection",
        ]),
      })
    )
    .mutation(async ({ input, ctx: { db, teamId, user } }) => {
      return upsertConstructionProject(db, {
        id: input.id,
        teamId: teamId!,
        userId: user.id,
        name: "", // This will be ignored in update
        currentPhase: input.currentPhase,
      });
    }),

  // Real-time collaboration endpoints
  subscribeToProjectUpdates: protectedProcedure
    .input(z.object({ projectId: z.string().uuid() }))
    .subscription(async ({ input, ctx: { db, teamId } }) => {
      // This would integrate with a real-time system like Supabase Realtime
      // For now, return a mock implementation
      return {
        subscribe: (callback: (update: any) => void) => {
          // Mock real-time updates
          return {
            unsubscribe: () => {},
          };
        },
      };
    }),

  // 3D model and file management
  uploadProjectFile: protectedProcedure
    .input(
      z.object({
        projectId: z.string().uuid(),
        fileName: z.string(),
        fileType: z.enum(["cad", "bim", "photo", "document", "other"]),
        fileSize: z.number(),
        filePath: z.string(),
      })
    )
    .mutation(async ({ input, ctx: { db, teamId, user } }) => {
      // This would handle file uploads to storage and update project files array
      // For now, return a mock implementation
      return {
        id: "file-id",
        fileName: input.fileName,
        fileType: input.fileType,
        uploadedAt: new Date().toISOString(),
      };
    }),

  // Dashboard and metrics
  getProjectMetrics: protectedProcedure
    .input(
      z.object({
        projectId: z.string().uuid().optional(),
        dateRange: z
          .object({
            start: z.string(),
            end: z.string(),
          })
          .optional(),
      })
    )
    .query(async ({ input, ctx: { db, teamId } }) => {
      // Calculate project metrics like progress, costs, timeline
      return {
        totalProjects: 0,
        activeProjects: 0,
        completedProjects: 0,
        totalEstimatedCost: 0,
        totalActualCost: 0,
        averageCompletion: 0,
        onTimeProjects: 0,
        delayedProjects: 0,
      };
    }),

  // Team presence for real-time collaboration
  getTeamPresence: protectedProcedure
    .input(z.object({ projectId: z.string().uuid() }))
    .query(async ({ input, ctx: { db, teamId } }) => {
      // Query real team presence data from the database
      const { teamPresence, users } = await import("@api/db/schema");
      const { eq, and } = await import("drizzle-orm");

      const presenceData = await db
        .select({
          id: teamPresence.id,
          user_id: teamPresence.userId,
          user_name: users.fullName,
          role: "team_member", // TODO: Get actual role from users_on_team
          is_online: teamPresence.isOnline,
          last_seen: teamPresence.lastSeen,
          current_view: teamPresence.currentView,
          current_position: teamPresence.currentPosition,
          current_activity: "Active", // TODO: Derive from current_view
          location: "On-site", // TODO: Get from project location or user location
        })
        .from(teamPresence)
        .leftJoin(users, eq(teamPresence.userId, users.id))
        .where(
          and(
            eq(teamPresence.projectId, input.projectId),
            eq(teamPresence.teamId, teamId!)
          )
        )
        .orderBy(teamPresence.lastSeen);

      return presenceData;
    }),
});