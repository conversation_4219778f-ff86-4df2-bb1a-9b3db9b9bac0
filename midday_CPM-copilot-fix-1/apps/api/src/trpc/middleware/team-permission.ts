import type { Database } from "@api/db";
import type { Session } from "@api/utils/auth";
import { TRPCError } from "@trpc/server";
import { LRUCache } from "lru-cache";

// In-memory cache to check if a user has access to a team
// Note: This cache is per server instance, and we typically run 1 instance per region.
// Otherwise, we would need to share this state with Redis or a similar external store.
const cache = new LRUCache<string, boolean>({
  max: 5_000, // up to 5k entries (adjust based on memory)
  ttl: 1000 * 60 * 30, // 30 minutes in milliseconds
});

export const withTeamPermission = async <TReturn>(opts: {
  ctx: {
    session?: Session | null;
    db: Database;
  };
  next: (opts: {
    ctx: {
      session?: Session | null;
      db: Database;
      teamId: string | null;
    };
  }) => Promise<TReturn>;
}) => {
  const { ctx, next } = opts;

  const userId = ctx.session?.user?.id;

  if (!userId) {
    throw new TRPCError({
      code: "UNAUTHORIZED",
      message: "No permission to access this team",
    });
  }

  // Development bypass: if we're in development and have the dev user
  if (process.env.BYPASS_AUTH === "true" && userId === "51efe2e4-531a-4fdc-9a12-d96284c69f6c") {
    return next({
      ctx: {
        session: ctx.session,
        teamId: "a8cd4d5d-95c0-4f3c-9f4e-bf945b775813",
        db: ctx.db,
      },
    });
  }

  let result;
  try {
    result = await ctx.db.query.users.findFirst({
      with: {
        usersOnTeams: {
          columns: {
            id: true,
            teamId: true,
          },
        },
      },
      where: (users, { eq }) => eq(users.id, userId),
    });
  } catch (error) {
    // Database connection error - provide helpful development message
    if (process.env.NODE_ENV === "development") {
      console.error("🚨 Database connection failed:", error instanceof Error ? error.message : String(error));
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Database connection failed. Please check DATABASE_SESSION_POOLER in .env.local",
      });
    }
    throw new TRPCError({
      code: "INTERNAL_SERVER_ERROR",
      message: "Database error",
    });
  }

  if (!result) {
    throw new TRPCError({
      code: "NOT_FOUND",
      message: "User not found",
    });
  }

  const teamId = result.teamId;

  // If teamId is null, user has no team assigned
  // This is allowed and we'll pass null teamId to the context
  if (teamId !== null) {
    const cacheKey = `user:${userId}:team:${teamId}`;
    let hasAccess = cache.get(cacheKey);

    if (hasAccess === undefined) {
      hasAccess = result.usersOnTeams.some(
        (membership) => membership.teamId === teamId,
      );

      cache.set(cacheKey, hasAccess);
    }

    if (!hasAccess) {
      throw new TRPCError({
        code: "FORBIDDEN",
        message: "No permission to access this team",
      });
    }
  }

  return next({
    ctx: {
      session: ctx.session,
      teamId,
      db: ctx.db,
    },
  });
};
