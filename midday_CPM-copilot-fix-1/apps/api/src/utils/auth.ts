import { type JWTPayload, jwtVerify } from "jose";

export type Session = {
  user: {
    id: string;
    email?: string;
    full_name?: string;
  };
  teamId?: string;
};

type SupabaseJWTPayload = JWTPayload & {
  user_metadata?: {
    email?: string;
    full_name?: string;
    [key: string]: string | undefined;
  };
};

export async function verifyAccessToken(
  accessToken?: string,
): Promise<Session | null> {
  // Development mode: Allow bypass when no token is provided and in development
  if (!accessToken && process.env.BYPASS_AUTH === "true") {
    return {
      user: {
        id: "51efe2e4-531a-4fdc-9a12-d96284c69f6c",
        email: "<EMAIL>",
        full_name: "Development User",
      },
      teamId: "a8cd4d5d-95c0-4f3c-9f4e-bf945b775813",
    };
  }

  if (!accessToken) return null;

  try {
    const { payload } = await jwtVerify(
      accessToken,
      new TextEncoder().encode(process.env.SUPABASE_JWT_SECRET),
    );

    const supabasePayload = payload as SupabaseJWTPayload;

    return {
      user: {
        id: supabasePayload.sub!,
        email: supabasePayload.user_metadata?.email,
        full_name: supabasePayload.user_metadata?.full_name,
      },
    };
  } catch (error) {
    return null;
  }
}
