import { drizzle } from "drizzle-orm/postgres-js";
import postgres from "postgres";

const connectionString = process.env.DATABASE_SESSION_POOLER!;

const sql = postgres(connectionString);
const db = drizzle(sql);

async function setupTables() {
  console.log("🚀 Setting up essential database tables...");
  
  try {
    // Create customers table
    await sql`
      CREATE TABLE IF NOT EXISTS "customers" (
        "id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
        "created_at" timestamp with time zone DEFAULT now() NOT NULL,
        "name" text NOT NULL,
        "email" text NOT NULL,
        "billingEmail" text,
        "country" text,
        "address_line_1" text,
        "address_line_2" text,
        "city" text,
        "state" text,
        "zip" text,
        "note" text,
        "team_id" uuid NOT NULL,
        "website" text,
        "phone" text,
        "vat_number" text,
        "country_code" text,
        "token" text DEFAULT '' NOT NULL,
        "contact" text
      )
    `;
    console.log("✅ Created customers table");

    // Create tags table
    await sql`
      CREATE TABLE IF NOT EXISTS "tags" (
        "id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
        "created_at" timestamp with time zone DEFAULT now() NOT NULL,
        "team_id" uuid NOT NULL,
        "name" text NOT NULL,
        CONSTRAINT "unique_tag_name" UNIQUE("team_id","name")
      )
    `;
    console.log("✅ Created tags table");

    // Create customer_tags table
    await sql`
      CREATE TABLE IF NOT EXISTS "customer_tags" (
        "id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
        "created_at" timestamp with time zone DEFAULT now() NOT NULL,
        "customer_id" uuid NOT NULL,
        "team_id" uuid NOT NULL,
        "tag_id" uuid NOT NULL,
        CONSTRAINT "unique_customer_tag" UNIQUE("customer_id","tag_id")
      )
    `;
    console.log("✅ Created customer_tags table");

    // Create invoices table
    await sql`
      CREATE TABLE IF NOT EXISTS "invoices" (
        "id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
        "created_at" timestamp with time zone DEFAULT now() NOT NULL,
        "updated_at" timestamp with time zone DEFAULT now(),
        "due_date" timestamp with time zone,
        "invoice_number" text,
        "customer_id" uuid,
        "amount" numeric(10, 2),
        "currency" text,
        "team_id" uuid NOT NULL,
        "status" text DEFAULT 'draft' NOT NULL
      )
    `;
    console.log("✅ Created invoices table");

    // Create tracker_projects table
    await sql`
      CREATE TABLE IF NOT EXISTS "tracker_projects" (
        "id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
        "created_at" timestamp with time zone DEFAULT now() NOT NULL,
        "team_id" uuid,
        "name" text NOT NULL,
        "customer_id" uuid
      )
    `;
    console.log("✅ Created tracker_projects table");

    // Create bank_connections table
    await sql`
      CREATE TABLE IF NOT EXISTS "bank_connections" (
        "id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
        "created_at" timestamp with time zone DEFAULT now() NOT NULL,
        "team_id" uuid NOT NULL,
        "name" text NOT NULL,
        "provider" text,
        "status" text DEFAULT 'active'
      )
    `;
    console.log("✅ Created bank_connections table");

    // Create bank_accounts table
    await sql`
      CREATE TABLE IF NOT EXISTS "bank_accounts" (
        "id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
        "created_at" timestamp with time zone DEFAULT now() NOT NULL,
        "team_id" uuid NOT NULL,
        "bank_connection_id" uuid,
        "name" text NOT NULL,
        "currency" text DEFAULT 'USD',
        "type" text DEFAULT 'checking'
      )
    `;
    console.log("✅ Created bank_accounts table");

    // Create transactions table
    await sql`
      CREATE TABLE IF NOT EXISTS "transactions" (
        "id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
        "created_at" timestamp with time zone DEFAULT now() NOT NULL,
        "team_id" uuid NOT NULL,
        "bank_account_id" uuid,
        "amount" numeric(10, 2),
        "currency" text DEFAULT 'USD',
        "date" timestamp with time zone,
        "description" text,
        "status" text DEFAULT 'posted'
      )
    `;
    console.log("✅ Created transactions table");

    console.log("🎉 All essential tables created successfully!");
    
  } catch (error) {
    console.error("❌ Error setting up tables:", error);
  } finally {
    await sql.end();
  }
}

setupTables();
