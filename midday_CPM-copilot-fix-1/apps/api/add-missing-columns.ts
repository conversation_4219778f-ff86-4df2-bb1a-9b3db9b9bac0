import postgres from "postgres";

const connectionString = process.env.DATABASE_SESSION_POOLER!;
const sql = postgres(connectionString);

async function addMissingColumns() {
  console.log("🔧 Adding missing columns to tables...");
  
  try {
    // Add billingEmail column to customers table
    await sql`
      ALTER TABLE customers 
      ADD COLUMN IF NOT EXISTS "billingEmail" text
    `;
    console.log("✅ Added billingEmail column to customers table");

    // Update token column to have default value
    await sql`
      ALTER TABLE customers 
      ALTER COLUMN token SET DEFAULT ''
    `;
    console.log("✅ Updated token column default value");

    // Check the updated structure
    const customersInfo = await sql`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'customers' 
      AND column_name IN ('billingEmail', 'token')
      ORDER BY ordinal_position
    `;
    console.log("📋 Updated columns:");
    console.table(customersInfo);

    console.log("🎉 Missing columns added successfully!");
    
  } catch (error) {
    console.error("❌ Error adding columns:", error);
  } finally {
    await sql.end();
  }
}

addMissingColumns();
