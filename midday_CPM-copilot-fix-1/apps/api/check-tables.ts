import postgres from "postgres";

const connectionString = process.env.DATABASE_SESSION_POOLER!;
const sql = postgres(connectionString);

async function checkTables() {
  console.log("🔍 Checking database tables...");
  
  try {
    // Check if customers table exists and get its structure
    const customersInfo = await sql`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'customers' 
      ORDER BY ordinal_position
    `;
    console.log("📋 Customers table structure:");
    console.table(customersInfo);

    // Check if other tables exist
    const tables = await sql`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name IN ('customers', 'tags', 'customer_tags', 'invoices', 'tracker_projects', 'bank_connections', 'bank_accounts', 'transactions')
      ORDER BY table_name
    `;
    console.log("📊 Available tables:");
    console.table(tables);

    // Try a simple select from customers
    const customerCount = await sql`SELECT COUNT(*) as count FROM customers`;
    console.log("👥 Customer count:", customerCount[0].count);

  } catch (error) {
    console.error("❌ Error checking tables:", error);
  } finally {
    await sql.end();
  }
}

checkTables();
