// Mock data provider for development when database is not available

export const mockTeam = {
  id: "a8cd4d5d-95c0-4f3c-9f4e-bf945b775813",
  name: "Development Team",
  email: "<EMAIL>",
  logo_url: null,
  inbox_id: null,
  inbox_forwarding: false,
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
};

export const mockUser = {
  id: "user-1",
  full_name: "Development User",
  avatar_url: null,
  email: "<EMAIL>",
  team_id: mockTeam.id,
  role: "owner",
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
};

export const mockConstructionProjects = [
  {
    id: "133d080b-f460-41fd-be6c-faf0552165ce",
    name: "Downtown Office Complex",
    description: "Modern office building with retail space",
    location: "Downtown Business District",
    address: "123 Business Ave, Downtown",
    status: "in_progress",
    completionPercentage: 66,
    startDate: "2024-01-15T00:00:00.000Z",
    endDate: "2024-12-15T00:00:00.000Z",
    currentPhase: "construction",
    estimatedCost: 2500000,
    actualCost: 1650000,
    currency: "USD",
    customer: { id: "customer-1", name: "ABC Corporation" },
    contractorInfo: {
      name: "BuildCorp Construction",
      contact: "John Smith",
      phone: "******-0123"
    },
    permitInfo: {
      number: "BP-2024-001",
      issuedDate: "2024-01-10",
      expiryDate: "2025-01-10"
    },
    siteCoordinates: { 
      latitude: 40.7128, 
      longitude: -74.0060,
      elevation: 10
    },
    team_id: mockTeam.id,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
  {
    id: "project-2",
    name: "Residential Complex Phase 1",
    description: "50-unit residential building with amenities",
    location: "Suburban District",
    address: "456 Residential Blvd, Suburbs",
    status: "planning",
    completionPercentage: 15,
    startDate: "2024-03-01T00:00:00.000Z",
    endDate: "2025-06-30T00:00:00.000Z",
    currentPhase: "planning",
    estimatedCost: 4200000,
    actualCost: 630000,
    currency: "USD",
    customer: { id: "customer-2", name: "Residential Developers Inc" },
    contractorInfo: {
      name: "HomeBuild Solutions",
      contact: "Sarah Johnson",
      phone: "******-0456"
    },
    permitInfo: {
      number: "BP-2024-002",
      issuedDate: "2024-02-20",
      expiryDate: "2025-02-20"
    },
    siteCoordinates: { 
      latitude: 40.7589, 
      longitude: -73.9851,
      elevation: 25
    },
    team_id: mockTeam.id,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  }
];

export const mockSiteMeasurements = [
  {
    id: "measurement-1",
    project_id: "133d080b-f460-41fd-be6c-faf0552165ce",
    measurement_type: "cut_volume",
    value: 250000,
    unit: "cubic_yards",
    coordinates: { lat: 40.7128, lng: -74.0060 },
    elevation: 10,
    created_at: new Date().toISOString(),
  },
  {
    id: "measurement-2", 
    project_id: "133d080b-f460-41fd-be6c-faf0552165ce",
    measurement_type: "fill_volume",
    value: 24000,
    unit: "cubic_yards",
    coordinates: { lat: 40.7128, lng: -74.0060 },
    elevation: 15,
    created_at: new Date().toISOString(),
  }
];

export const mockTeamPresence = [
  {
    id: "presence-1",
    user_id: "user-1",
    project_id: "133d080b-f460-41fd-be6c-faf0552165ce",
    user_name: "John Doe",
    role: "project_manager",
    is_online: true,
    last_seen: new Date().toISOString(),
    current_activity: "Reviewing blueprints",
    location: "Site Office",
  },
  {
    id: "presence-2",
    user_id: "user-2", 
    project_id: "133d080b-f460-41fd-be6c-faf0552165ce",
    user_name: "Sarah Miller",
    role: "site_engineer",
    is_online: true,
    last_seen: new Date().toISOString(),
    current_activity: "Foundation inspection",
    location: "Building A",
  }
];

export const mockBankAccounts = [];
export const mockTransactions = [];
export const mockInvoices = [];

// Helper function to check if we should use mock data
export function shouldUseMockData(): boolean {
  return process.env.NODE_ENV === "development" && 
         process.env.NEXT_PUBLIC_BYPASS_AUTH === "true";
}

// Mock data providers for tRPC queries
export const mockDataProviders = {
  constructionProjects: {
    getAll: () => mockConstructionProjects,
    getById: (id: string) => mockConstructionProjects.find(p => p.id === id),
    getTeamPresence: (projectId: string) => mockTeamPresence.filter(p => p.project_id === projectId),
  },
  siteMeasurements: {
    getByProject: (projectId: string) => mockSiteMeasurements.filter(m => m.project_id === projectId),
  },
  team: {
    current: () => mockTeam,
  },
  user: {
    current: () => mockUser,
  },
  bankAccounts: {
    get: () => mockBankAccounts,
  },
  transactions: {
    get: () => mockTransactions,
  },
  invoice: {
    get: () => mockInvoices,
    defaultSettings: () => ({
      template: "default",
      currency: "USD",
      from_details: {
        name: "Development Company",
        address: "123 Dev Street",
        city: "Dev City",
        country: "US"
      }
    }),
  },
  search: {
    global: () => ({ data: [], meta: { count: 0 } }),
  }
};

// Error handler for database connection issues
export function handleDatabaseError(error: any, fallbackData: any = null) {
  console.warn("Database connection issue, using mock data:", error?.message);
  return fallbackData;
}

// Development mode query wrapper
export function createMockQuery<T>(
  queryFn: () => Promise<T>,
  mockData: T,
  enabled: boolean = true
) {
  if (!enabled) {
    return { data: undefined, isLoading: false, error: null };
  }

  if (shouldUseMockData()) {
    return { 
      data: mockData, 
      isLoading: false, 
      error: null,
      isSuccess: true,
      isError: false
    };
  }

  // In production or when database is available, use real query
  return queryFn().then(data => ({
    data,
    isLoading: false,
    error: null,
    isSuccess: true,
    isError: false
  })).catch(error => ({
    data: mockData,
    isLoading: false,
    error,
    isSuccess: false,
    isError: true
  }));
}
