import { ChartSelectors } from "@/components/charts/chart-selectors";
import { Charts } from "@/components/charts/charts";
import { EmptyState } from "@/components/charts/empty-state";
import { OverviewModal } from "@/components/modals/overview-modal";
import { Widgets } from "@/components/widgets";
import { defaultPeriod } from "@/components/widgets/spending/data";
import { loadMetricsParams } from "@/hooks/use-metrics-params";
import { HydrateClient, batchPrefetch, trpc } from "@/trpc/server";
import { getQueryClient } from "@/trpc/server";
import { Cookies } from "@/utils/constants";
import { cn } from "@midday/ui/cn";
import type { Metadata } from "next";
import { cookies } from "next/headers";
import type { SearchParams } from "nuqs";

export const metadata: Metadata = {
  title: "Overview | Midday",
};

type Props = {
  searchParams: Promise<SearchParams>;
};

export default async function Overview(props: Props) {
  const queryClient = getQueryClient();
  const searchParams = await props.searchParams;
  const { from, to } = loadMetricsParams(searchParams);

  const cookieStore = await cookies();
  const hideConnectFlow =
    cookieStore.get(Cookies.HideConnectFlow)?.value === "true";

  // Wrap prefetch in try-catch to handle database schema issues gracefully
  try {
    batchPrefetch([
      // Only prefetch data that we know exists or can handle gracefully
      trpc.metrics.expense.queryOptions({ from, to }),
      trpc.metrics.profit.queryOptions({ from, to }),
      trpc.metrics.burnRate.queryOptions({ from, to }),
      trpc.metrics.runway.queryOptions({ from, to }),
      trpc.metrics.spending.queryOptions({
        from: defaultPeriod.from,
        to: defaultPeriod.to,
      }),
      // Skip problematic queries that might fail due to missing tables
      // trpc.invoice.get.queryOptions({ pageSize: 10 }),
      // trpc.invoice.paymentStatus.queryOptions(),
      // trpc.inbox.get.queryOptions(),
      // trpc.bankAccounts.balances.queryOptions(),
      // trpc.documents.get.queryOptions({ pageSize: 10 }),
      // trpc.transactions.get.queryOptions({ pageSize: 15 }),
    ]);
  } catch (error) {
    console.warn("Failed to prefetch dashboard data:", error);
  }

  // Skip server-side data fetching for now to avoid database errors
  // The client-side components will handle loading states gracefully
  const isEmpty = false; // Let client-side components determine if empty

  // Only prefetch metrics data that we know works
  try {
    await queryClient.fetchQuery(
      trpc.metrics.revenue.queryOptions({
        from,
        to,
      }),
    ).catch(() => null); // Ignore errors for metrics
  } catch (error) {
    console.warn("Failed to fetch metrics data:", error);
  }

  return (
    <HydrateClient>
      <div>
        <div className="h-[530px] mb-4">
          <ChartSelectors />

          <div className="mt-8 relative">
            {/* EmptyState will be handled by client-side components */}
            <Charts disabled={false} />
          </div>
        </div>

        <Widgets disabled={false} />
      </div>

      <OverviewModal defaultOpen={!hideConnectFlow} />
    </HydrateClient>
  );
}
