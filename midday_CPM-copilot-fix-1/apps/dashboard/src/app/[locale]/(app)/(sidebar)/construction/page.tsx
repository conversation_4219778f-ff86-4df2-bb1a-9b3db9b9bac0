"use client";

import { ConstructionProjectCard } from "@/components/construction-project-card";
import { ConstructionProjectForm } from "@/components/forms/construction-project-form";
import { mockDataProviders, shouldUseMockData } from "@/lib/mock-data-provider";
import { useTRPC } from "@/trpc/client";
import { <PERSON><PERSON> } from "@midday/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@midday/ui/dialog";
import { Input } from "@midday/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@midday/ui/select";
import { Building2, Plus, Search } from "lucide-react";
import { useState } from "react";
import { useRouter } from "next/navigation";

export default function ConstructionPage() {
  const [search, setSearch] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const router = useRouter();

  // Use mock data in development mode when database is not available
  const useMockData = shouldUseMockData();

  const { data, isLoading, error } = useTRPC.constructionProjects?.get?.useQuery({
    q: search || null,
    status: statusFilter === "all" ? null : statusFilter as any,
    pageSize: 50,
  }, {
    enabled: !!useTRPC.constructionProjects?.get && !useMockData,
    retry: false,
  }) || { data: null, isLoading: false, error: null };

  // Use mock data if in development mode or if query failed
  const mockProjects = useMockData ? mockDataProviders.constructionProjects.getAll() : [];
  const projects = data?.data || mockProjects;

  const handleProjectClick = (projectId: string) => {
    router.push(`/construction/${projectId}`);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Construction Projects</h1>
          <p className="text-muted-foreground">
            Manage your construction projects, track progress, and collaborate with your team
          </p>
        </div>
        
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              New Project
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Create Construction Project</DialogTitle>
            </DialogHeader>
            <ConstructionProjectForm 
              onSuccess={() => setIsCreateDialogOpen(false)}
            />
          </DialogContent>
        </Dialog>
      </div>

      {/* Filters */}
      <div className="flex items-center space-x-4">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder="Search projects..."
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className="pl-10"
          />
        </div>
        
        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-48">
            <SelectValue placeholder="Filter by status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Status</SelectItem>
            <SelectItem value="planning">Planning</SelectItem>
            <SelectItem value="in_progress">In Progress</SelectItem>
            <SelectItem value="on_hold">On Hold</SelectItem>
            <SelectItem value="completed">Completed</SelectItem>
            <SelectItem value="cancelled">Cancelled</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Project Grid */}
      {isLoading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <div key={i} className="h-64 bg-gray-100 rounded-lg animate-pulse" />
          ))}
        </div>
      ) : error ? (
        <div className="text-center py-12">
          <Building2 className="h-12 w-12 mx-auto text-red-500 mb-4" />
          <h3 className="text-lg font-semibold mb-2">Error loading projects</h3>
          <p className="text-muted-foreground mb-4">
            {error.message || "Failed to load construction projects"}
          </p>
          <Button onClick={() => window.location.reload()}>
            Try Again
          </Button>
        </div>
      ) : projects.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {projects.map((project) => (
            <ConstructionProjectCard
              key={project.id}
              project={project}
              onClick={() => handleProjectClick(project.id)}
            />
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <Building2 className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
          <h3 className="text-lg font-semibold mb-2">No construction projects found</h3>
          <p className="text-muted-foreground mb-4">
            {search || statusFilter !== "all" 
              ? "Try adjusting your search or filters"
              : "Create your first construction project to get started"}
          </p>
          {!search && statusFilter === "all" && (
            <Button onClick={() => setIsCreateDialogOpen(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Create Project
            </Button>
          )}
        </div>
      )}

      {/* Stats Overview */}
      {projects.length > 0 && (
        <div className="mt-8 grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border">
            <div className="text-2xl font-bold">
              {projects.filter(p => p.status === "in_progress").length}
            </div>
            <div className="text-sm text-muted-foreground">Active Projects</div>
          </div>
          <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border">
            <div className="text-2xl font-bold">
              {Math.round(
                projects.reduce((acc, p) => acc + (p.completionPercentage || 0), 0) / 
                projects.length
              )}%
            </div>
            <div className="text-sm text-muted-foreground">Average Progress</div>
          </div>
          <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border">
            <div className="text-2xl font-bold">
              {projects.filter(p => p.status === "completed").length}
            </div>
            <div className="text-sm text-muted-foreground">Completed</div>
          </div>
          <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border">
            <div className="text-2xl font-bold">
              ${projects
                .reduce((acc, p) => acc + (p.estimatedCost || 0), 0)
                .toLocaleString()}
            </div>
            <div className="text-sm text-muted-foreground">Total Value</div>
          </div>
        </div>
      )}
    </div>
  );
}