import { BaseCurrency } from "@/components/base-currency/base-currency";
import { ConnectedAccounts } from "@/components/connected-accounts";
import { prefetch, trpc } from "@/trpc/server";
import type { Metadata } from "next";

export const metadata: Metadata = {
  title: "Accounts | Midday",
};

export default async function Page() {
  // Skip problematic prefetch calls to avoid database errors
  // prefetch(trpc.bankConnections.get.queryOptions());
  // prefetch(trpc.bankAccounts.get.queryOptions({ manual: true }));

  // Only prefetch data that we know works
  try {
    prefetch(trpc.team.current.queryOptions());
  } catch (error) {
    console.warn("Failed to prefetch team data:", error);
  }

  return (
    <div className="space-y-12">
      <ConnectedAccounts />
      <BaseCurrency />
    </div>
  );
}
