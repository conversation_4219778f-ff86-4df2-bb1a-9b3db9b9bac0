import { ExportStatus } from "@/components/export-status";
import { Head<PERSON> } from "@/components/header";
import { GlobalSheets } from "@/components/sheets/global-sheets";
import { Sidebar } from "@/components/sidebar";
import {
  HydrateClient,
  batchPrefetch,
  getQueryClient,
  trpc,
} from "@/trpc/server";
import { getCountryCode, getCurrency } from "@midday/location";
import { redirect } from "next/navigation";
import { Suspense } from "react";

export default async function Layout({
  children,
}: {
  children: React.ReactNode;
}) {
  const queryClient = getQueryClient();
  const currencyPromise = getCurrency();
  const countryCodePromise = getCountryCode();

  // NOTE: Right now we want to fetch the user and hydrate the client
  // Next steps would be to prefetch and suspense
  const user = await queryClient.fetchQuery(trpc.user.me.queryOptions());

  if (!user) {
    redirect("/login");
  }

  if (!user.fullName) {
    redirect("/setup");
  }

  if (!user.teamId) {
    redirect("/teams");
  }

  // NOTE: These are used in the global sheets
  // Only prefetch team-related data if user has a team
  // Wrap in try-catch to handle database connection issues gracefully
  try {
    // Only prefetch essential data that we know exists
    batchPrefetch([
      trpc.team.current.queryOptions(),
      // Skip invoice and search prefetch for now to avoid database errors
      // trpc.invoice.defaultSettings.queryOptions(),
      // trpc.search.global.queryOptions({ searchTerm: "" }),
    ]);
  } catch (error) {
    console.warn("Failed to prefetch data, continuing with client-side loading:", error);
  }

  return (
    <HydrateClient>
      <div className="relative">
        <Sidebar />

        <div className="md:ml-[70px] pb-8">
          <Header />
          <div className="px-6">{children}</div>
        </div>

        <ExportStatus />

        <Suspense>
          <GlobalSheets
            currencyPromise={currencyPromise}
            countryCodePromise={countryCodePromise}
          />
        </Suspense>
      </div>
    </HydrateClient>
  );
}
