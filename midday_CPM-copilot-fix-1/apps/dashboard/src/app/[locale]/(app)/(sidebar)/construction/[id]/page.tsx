"use client";

import { ConstructionSiteViewer } from "@/components/construction/site-viewer";
import { ConstructionProjectDetails } from "@/components/construction/project-details";
import { ConstructionAnalytics } from "@/components/construction/analytics";
import { ConstructionTeamPresence } from "@/components/construction/team-presence";
import { ConstructionActivityFeed } from "@/components/construction/activity-feed";
import { ConstructionMobileNav } from "@/components/construction/mobile-nav";
import { RoleGuard, CanViewFinancials, EngineerOrManager } from "@/components/construction/role-guard";
import { useConstructionPermissions } from "@/hooks/use-construction-permissions";
import { ConstructionRole } from "@/lib/construction-roles";
import { useTRPC } from "@/trpc/client";
import { useParams, useRouter } from "next/navigation";
import { useState, useEffect } from "react";
import { Button } from "@midday/ui/button";
import { ArrowLeft } from "lucide-react";
import { useUserQuery } from "@/hooks/use-user";

export default function ConstructionProjectPage() {
  const params = useParams();
  const router = useRouter();
  const projectId = params.id as string;
  const [isClient, setIsClient] = useState(false);

  // Get current user to determine role
  const { data: user } = useUserQuery();

  // TODO: Get user's construction role from team membership or project assignment
  // For now, defaulting to project_manager - this should be determined from database
  const userRole: ConstructionRole = "project_manager";

  useEffect(() => {
    setIsClient(true);
  }, []);

  // Real database queries - no mock data
  const api = useTRPC();

  const { data: project, isLoading } = api.constructionProjects?.getById?.useQuery({
    id: projectId,
  }, {
    enabled: isClient && !!projectId && !!api.constructionProjects?.getById,
    retry: 1,
  }) || { data: null, isLoading: false };

  const { data: siteMeasurements } = api.siteMeasurements?.getByProject?.useQuery({
    projectId,
  }, {
    enabled: isClient && !!projectId && !!api.siteMeasurements?.getByProject,
    retry: 1,
  }) || { data: null };

  const { data: teamPresence } = api.constructionProjects?.getTeamPresence?.useQuery({
    projectId,
  }, {
    enabled: isClient && !!projectId && !!api.constructionProjects?.getTeamPresence,
    retry: 1,
  }) || { data: null };

  // Get role-based permissions
  const permissions = useConstructionPermissions({
    userRole,
    projectContext: {
      isProjectOwner: project?.createdBy === user?.id,
      isTeamMember: project?.teamId === user?.teamId,
      projectPhase: project?.currentPhase,
      projectId
    }
  });

  if (!isClient || isLoading) {
    return (
      <div className="h-screen flex">
        {/* Left Panel Skeleton */}
        <div className="w-80 bg-background border-r p-6 space-y-4">
          <div className="h-6 bg-gray-200 rounded animate-pulse" />
          <div className="h-4 bg-gray-200 rounded animate-pulse w-3/4" />
          <div className="space-y-2">
            <div className="h-3 bg-gray-200 rounded animate-pulse" />
            <div className="h-3 bg-gray-200 rounded animate-pulse w-1/2" />
          </div>
        </div>

        {/* Main Viewer Skeleton */}
        <div className="flex-1 bg-gray-100 animate-pulse" />

        {/* Bottom Panel Skeleton */}
        <div className="absolute bottom-0 left-80 right-0 h-64 bg-background border-t p-4">
          <div className="h-4 bg-gray-200 rounded animate-pulse w-1/4 mb-4" />
          <div className="grid grid-cols-4 gap-4">
            <div className="h-20 bg-gray-200 rounded animate-pulse" />
            <div className="h-20 bg-gray-200 rounded animate-pulse" />
            <div className="h-20 bg-gray-200 rounded animate-pulse" />
            <div className="h-20 bg-gray-200 rounded animate-pulse" />
          </div>
        </div>
      </div>
    );
  }

  if (!project) {
    return (
      <div className="h-screen flex items-center justify-center">
        <div className="text-center">
          <h3 className="text-lg font-semibold mb-2">Project not found</h3>
          <p className="text-muted-foreground mb-4">
            The construction project you're looking for doesn't exist or you don't have access to it.
          </p>
          <Button onClick={() => router.push("/construction")}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Projects
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen flex flex-col overflow-hidden">
      {/* Mobile Navigation */}
      <ConstructionMobileNav
        project={project}
        siteMeasurements={siteMeasurements || []}
        teamPresence={teamPresence || []}
        projectId={projectId}
        userRole={userRole}
        permissions={permissions}
        onBack={() => router.push("/construction")}
      />

      {/* Three-Panel Layout matching the UI design */}
      <div className="flex flex-1 relative pt-16 lg:pt-0 pb-16 lg:pb-0">
        {/* Left Panel - Project Details (Hidden on mobile, shown as drawer) */}
        <div className="hidden lg:flex w-80 bg-background border-r flex-col">
          <ConstructionProjectDetails
            project={project}
            siteMeasurements={siteMeasurements}
            onBack={() => router.push("/construction")}
            userRole={userRole}
            permissions={permissions}
          />
        </div>

        {/* Main Center - Site Viewer */}
        <div className="flex-1 relative bg-gray-900">
          <ConstructionSiteViewer
            project={project}
            siteMeasurements={siteMeasurements}
          />

          {/* Team Presence Overlay - Hidden on mobile */}
          <div className="hidden lg:block absolute top-4 right-4 space-y-4">
            <EngineerOrManager userRole={userRole}>
              <ConstructionTeamPresence
                projectId={projectId}
                teamPresence={teamPresence}
                userRole={userRole}
              />
            </EngineerOrManager>

            <RoleGuard
              userRole={userRole}
              requiredPermissions={["view_project"]}
              showFallback={false}
            >
              <ConstructionActivityFeed
                projectId={projectId}
              />
            </RoleGuard>
          </div>
        </div>
      </div>

      {/* Bottom Panel - Analytics (Responsive height) */}
      <div className="h-48 md:h-64 bg-background border-t">
        <RoleGuard
          userRole={userRole}
          requiredPermissions={["view_project"]}
          fallback={
            <div className="h-full flex items-center justify-center text-muted-foreground">
              <div className="text-center">
                <div className="text-sm">Analytics view restricted</div>
                <div className="text-xs">Contact your project manager for access</div>
              </div>
            </div>
          }
        >
          <ConstructionAnalytics
            project={project}
            siteMeasurements={siteMeasurements}
            userRole={userRole}
            permissions={permissions}
          />
        </RoleGuard>
      </div>
    </div>

  );
}