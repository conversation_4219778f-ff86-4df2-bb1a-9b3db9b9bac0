"use client";

import { GoogleMap, Load<PERSON>, <PERSON><PERSON>, <PERSON>y<PERSON>, InfoWindow } from "@react-google-maps/api";
import { But<PERSON> } from "@midday/ui/button";
import { Badge } from "@midday/ui/badge";
import { Card, CardContent } from "@midday/ui/card";
import { ZoomIn, ZoomOut, Plus, Minus, X, MapPin, Camera, AlertTriangle } from "lucide-react";
import { useState, useCallback, useEffect } from "react";

interface SiteViewerProps {
  project: any;
  siteMeasurements: any[];
}

const mapContainerStyle = {
  width: '100%',
  height: '100%'
};

// Default center - you can get this from project.siteCoordinates
const defaultCenter = {
  lat: 40.7128,
  lng: -74.0060
};

const mapOptions = {
  disableDefaultUI: true,
  zoomControl: false,
  mapTypeControl: false,
  streetViewControl: false,
  fullscreenControl: false,
  styles: [
    {
      "featureType": "all",
      "elementType": "geometry",
      "stylers": [{"color": "#1f2937"}]
    },
    {
      "featureType": "all",
      "elementType": "labels.text.stroke",
      "stylers": [{"lightness": -80}]
    },
    {
      "featureType": "administrative",
      "elementType": "labels.text.fill",
      "stylers": [{"color": "#6b7280"}]
    },
    {
      "featureType": "road",
      "elementType": "geometry",
      "stylers": [{"color": "#374151"}]
    },
    {
      "featureType": "water",
      "elementType": "geometry",
      "stylers": [{"color": "#111827"}]
    }
  ]
};

// Mock building data - in real app this would come from project data
const buildings = [
  {
    id: "N222",
    position: { lat: 40.7130, lng: -74.0058 },
    progress: 76,
    status: "in_progress",
    name: "Building N222",
    type: "residential",
    workers: 12,
    alerts: 1,
    lastUpdate: "2 hours ago"
  },
  {
    id: "N221",
    position: { lat: 40.7126, lng: -74.0062 },
    progress: 45,
    status: "in_progress",
    name: "Building N221",
    type: "commercial",
    workers: 8,
    alerts: 0,
    lastUpdate: "4 hours ago"
  },
  {
    id: "Kometsu",
    position: { lat: 40.7128, lng: -74.0060 },
    progress: 66,
    status: "highlighted",
    name: "Kometsu Main",
    type: "mixed_use",
    workers: 24,
    alerts: 2,
    lastUpdate: "30 minutes ago"
  }
];

// Mock equipment data
const equipment = [
  {
    id: "crane-1",
    position: { lat: 40.7129, lng: -74.0059 },
    type: "Tower Crane",
    status: "active",
    operator: "John Smith"
  },
  {
    id: "excavator-1",
    position: { lat: 40.7127, lng: -74.0061 },
    type: "Excavator",
    status: "idle",
    operator: "Mike Johnson"
  }
];

export function ConstructionSiteViewer({ project, siteMeasurements }: SiteViewerProps) {
  const [map, setMap] = useState<google.maps.Map | null>(null);
  const [zoom, setZoom] = useState(18);
  const [selectedBuilding, setSelectedBuilding] = useState<string | null>("Kometsu");
  const [showEquipment, setShowEquipment] = useState(true);
  const [showProgress, setShowProgress] = useState(true);
  const [mapType, setMapType] = useState<"roadmap" | "satellite" | "hybrid">("satellite");
  const [isGoogleMapsLoaded, setIsGoogleMapsLoaded] = useState(false);

  const onLoad = useCallback((map: google.maps.Map) => {
    setMap(map);
  }, []);

  const onUnmount = useCallback(() => {
    setMap(null);
  }, []);

  const onLoadScript = useCallback(() => {
    setIsGoogleMapsLoaded(true);
  }, []);

  const handleZoomIn = () => {
    if (map) {
      const newZoom = Math.min(zoom + 1, 22);
      setZoom(newZoom);
      map.setZoom(newZoom);
    }
  };

  const handleZoomOut = () => {
    if (map) {
      const newZoom = Math.max(zoom - 1, 10);
      setZoom(newZoom);
      map.setZoom(newZoom);
    }
  };

  const center = project.siteCoordinates 
    ? { lat: project.siteCoordinates.lat, lng: project.siteCoordinates.lng }
    : defaultCenter;

  // Site boundary polygon (mock data)
  const siteBoundary = [
    { lat: 40.7135, lng: -74.0065 },
    { lat: 40.7135, lng: -74.0055 },
    { lat: 40.7120, lng: -74.0055 },
    { lat: 40.7120, lng: -74.0065 },
  ];

  return (
    <div className="relative w-full h-full">
      <LoadScript
        googleMapsApiKey={process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY || ""}
        onLoad={onLoadScript}
      >
        <GoogleMap
          mapContainerStyle={mapContainerStyle}
          center={center}
          zoom={zoom}
          onLoad={onLoad}
          onUnmount={onUnmount}
          options={{
            ...mapOptions,
            mapTypeId: mapType,
          }}
        >
          {/* Site Boundary */}
          <Polygon
            paths={siteBoundary}
            options={{
              fillColor: "#f59e0b",
              fillOpacity: 0.1,
              strokeColor: "#f59e0b",
              strokeOpacity: 0.8,
              strokeWeight: 2,
            }}
          />

          {/* Building Markers */}
          {isGoogleMapsLoaded && buildings.map((building) => (
            <Marker
              key={building.id}
              position={building.position}
              icon={{
                path: google.maps.SymbolPath.CIRCLE,
                scale: building.status === "highlighted" ? 25 : 20,
                fillColor: building.status === "highlighted" ? "#f59e0b" : "#6b7280",
                fillOpacity: 0.9,
                strokeColor: "#ffffff",
                strokeWeight: 2,
              }}
              onClick={() => setSelectedBuilding(building.id)}
            />
          ))}

          {/* Equipment Markers */}
          {isGoogleMapsLoaded && showEquipment && equipment.map((item) => (
            <Marker
              key={item.id}
              position={item.position}
              icon={{
                path: google.maps.SymbolPath.BACKWARD_CLOSED_ARROW,
                scale: 15,
                fillColor: item.status === "active" ? "#10b981" : "#f59e0b",
                fillOpacity: 0.8,
                strokeColor: "#ffffff",
                strokeWeight: 1,
                rotation: 45,
              }}
              title={`${item.type} - ${item.status}`}
            />
          ))}

          {/* Progress Indicators */}
          {isGoogleMapsLoaded && showProgress && buildings.map((building) => (
            <InfoWindow
              key={`progress-${building.id}`}
              position={{
                lat: building.position.lat + 0.0002,
                lng: building.position.lng
              }}
              options={{
                disableAutoPan: true,
                pixelOffset: new google.maps.Size(0, -10)
              }}
            >
              <div className="bg-white p-2 rounded shadow-sm">
                <div className="text-xs font-medium">{building.progress}%</div>
              </div>
            </InfoWindow>
          ))}
        </GoogleMap>
      </LoadScript>

      {/* Building Info Cards */}
      {buildings.map((building) => (
        <div
          key={`info-${building.id}`}
          className={`absolute bg-background border rounded-lg p-3 shadow-lg transition-all ${
            building.id === "N222" ? "top-32 right-32" :
            building.id === "N221" ? "bottom-32 right-48" :
            "bottom-40 left-1/2 transform -translate-x-1/2"
          } ${selectedBuilding === building.id ? "ring-2 ring-orange-500" : ""}`}
          style={{
            minWidth: building.id === "Kometsu" ? "200px" : "120px"
          }}
        >
          {building.id !== "Kometsu" && (
            <Button
              variant="ghost"
              size="sm"
              className="absolute -top-2 -right-2 h-6 w-6 p-0"
              onClick={() => setSelectedBuilding(null)}
            >
              <Plus className="h-3 w-3 rotate-45" />
            </Button>
          )}
          
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="font-medium text-sm">{building.id}</span>
              {building.id !== "Kometsu" && (
                <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                  <Plus className="h-3 w-3" />
                </Button>
              )}
            </div>
            
            {building.id === "Kometsu" && (
              <>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-orange-500 rounded-full animate-pulse" />
                  <span className="text-sm font-medium">{building.progress}%</span>
                </div>
                
                {/* Mini progress chart */}
                <div className="space-y-1">
                  <div className="flex justify-between text-xs text-muted-foreground">
                    <span>Progress</span>
                    <span>{building.progress}%</span>
                  </div>
                  <div className="h-1 bg-gray-200 rounded-full overflow-hidden">
                    <div 
                      className="h-full bg-orange-500 transition-all duration-300"
                      style={{ width: `${building.progress}%` }}
                    />
                  </div>
                  
                  {/* Mini bar chart */}
                  <div className="flex items-end gap-1 h-8 mt-2">
                    {[20, 35, 45, 30, 55, 40, 60, 45, 35, 50, 65, 55].map((height, i) => (
                      <div
                        key={i}
                        className="bg-orange-400 rounded-sm flex-1"
                        style={{ height: `${height}%` }}
                      />
                    ))}
                  </div>
                </div>
              </>
            )}
          </div>
        </div>
      ))}

      {/* Interactive Controls - Hidden on mobile */}
      <div className="hidden md:block absolute top-4 right-4 space-y-2">
        {/* Map Type Selector */}
        <Card className="bg-background/95 backdrop-blur-sm">
          <CardContent className="p-2">
            <div className="flex gap-1">
              <Button
                variant={mapType === "satellite" ? "default" : "ghost"}
                size="sm"
                className="h-8 px-2 text-xs"
                onClick={() => setMapType("satellite")}
              >
                Satellite
              </Button>
              <Button
                variant={mapType === "roadmap" ? "default" : "ghost"}
                size="sm"
                className="h-8 px-2 text-xs"
                onClick={() => setMapType("roadmap")}
              >
                Map
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Layer Controls */}
        <Card className="bg-background/95 backdrop-blur-sm">
          <CardContent className="p-2 space-y-2">
            <div className="flex items-center gap-2">
              <input
                type="checkbox"
                id="equipment"
                checked={showEquipment}
                onChange={(e) => setShowEquipment(e.target.checked)}
                className="w-3 h-3"
              />
              <label htmlFor="equipment" className="text-xs">Equipment</label>
            </div>
            <div className="flex items-center gap-2">
              <input
                type="checkbox"
                id="progress"
                checked={showProgress}
                onChange={(e) => setShowProgress(e.target.checked)}
                className="w-3 h-3"
              />
              <label htmlFor="progress" className="text-xs">Progress</label>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Zoom Controls - Responsive positioning */}
      <div className="absolute bottom-4 right-4 md:bottom-4 md:right-4 flex flex-col gap-2">
        <Button
          variant="secondary"
          size="sm"
          className="h-10 w-10 p-0 md:h-10 md:w-10"
          onClick={handleZoomIn}
        >
          <Plus className="h-4 w-4" />
        </Button>
        <Button
          variant="secondary"
          size="sm"
          className="h-10 w-10 p-0 md:h-10 md:w-10"
          onClick={handleZoomOut}
        >
          <Minus className="h-4 w-4" />
        </Button>
      </div>

      {/* Mobile Map Controls */}
      <div className="md:hidden absolute top-4 left-4 right-4">
        <div className="flex justify-between items-center">
          <div className="flex gap-1">
            <Button
              variant={mapType === "satellite" ? "default" : "secondary"}
              size="sm"
              className="h-8 px-3 text-xs"
              onClick={() => setMapType("satellite")}
            >
              Satellite
            </Button>
            <Button
              variant={mapType === "roadmap" ? "default" : "secondary"}
              size="sm"
              className="h-8 px-3 text-xs"
              onClick={() => setMapType("roadmap")}
            >
              Map
            </Button>
          </div>

          <div className="flex gap-1">
            <Button
              variant="secondary"
              size="sm"
              className="h-8 w-8 p-0"
              onClick={() => setShowEquipment(!showEquipment)}
            >
              🚜
            </Button>
            <Button
              variant="secondary"
              size="sm"
              className="h-8 w-8 p-0"
              onClick={() => setShowProgress(!showProgress)}
            >
              📊
            </Button>
          </div>
        </div>
      </div>

      {/* Legend - Hidden on mobile */}
      <Card className="hidden md:block absolute bottom-4 left-4 bg-background/95 backdrop-blur-sm">
        <CardContent className="p-3">
          <div className="space-y-2">
            <div className="text-xs font-medium">Legend</div>
            <div className="space-y-1">
              <div className="flex items-center gap-2 text-xs">
                <div className="w-3 h-3 bg-orange-500 rounded-full" />
                <span>Active Building</span>
              </div>
              <div className="flex items-center gap-2 text-xs">
                <div className="w-3 h-3 bg-gray-500 rounded-full" />
                <span>Building</span>
              </div>
              <div className="flex items-center gap-2 text-xs">
                <div className="w-3 h-3 bg-green-500 rounded-full" />
                <span>Active Equipment</span>
              </div>
              <div className="flex items-center gap-2 text-xs">
                <div className="w-3 h-3 bg-orange-400 rounded-full" />
                <span>Idle Equipment</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
