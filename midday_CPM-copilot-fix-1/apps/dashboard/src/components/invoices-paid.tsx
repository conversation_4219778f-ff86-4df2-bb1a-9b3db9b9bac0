"use client";

import { useInvoiceFilterParams } from "@/hooks/use-invoice-filter-params";
import { useTRPC } from "@/trpc/client";
import { useQuery } from "@tanstack/react-query";
import { InvoiceSummary } from "./invoice-summary";

export function InvoicesPaid() {
  const { setFilter } = useInvoiceFilterParams();
  const trpc = useTRPC();

  // Use regular useQuery instead of useSuspenseQuery to handle errors gracefully
  const { data, error, isLoading } = useQuery({
    ...trpc.invoice.invoiceSummary.queryOptions({
      status: "paid",
    }),
    retry: false,
    refetchOnWindowFocus: false,
  });

  // If there's a database error or loading, show fallback
  if (error || isLoading || !data) {
    return (
      <div className="hidden sm:block text-left">
        <div className="p-4 text-sm text-muted-foreground">
          {error ? "Invoice data unavailable" : "Loading..."}
        </div>
      </div>
    );
  }

  const totalInvoiceCount = data?.at(0)?.invoiceCount;

  return (
    <button
      type="button"
      onClick={() =>
        setFilter({
          statuses: ["paid"],
        })
      }
      className="hidden sm:block text-left"
    >
      <InvoiceSummary
        data={data}
        totalInvoiceCount={totalInvoiceCount ?? 0}
        title="Paid"
      />
    </button>
  );
}
