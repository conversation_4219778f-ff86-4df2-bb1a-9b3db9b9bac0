"use client";

import { useState, useEffect, useRef } from "react";
import { Html } from "@react-three/drei";
import { use<PERSON>rame, useThree } from "@react-three/fiber";
import * as THREE from "three";
import { Avatar, AvatarFallback, AvatarImage } from "@midday/ui/avatar";
import { Badge } from "@midday/ui/badge";
import { Button } from "@midday/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@midday/ui/card";
import { Users, Eye, MessageCircle, Video, Mic, MicOff } from "lucide-react";

interface TeamMember {
  id: string;
  name: string;
  avatar?: string;
  isOnline: boolean;
  currentView: string;
  position?: { x: number; y: number; z: number };
  lastSeen: string;
  role: string;
  deviceInfo?: {
    type: "desktop" | "mobile" | "tablet";
    browser: string;
  };
}

interface Props {
  projectId: string;
  currentUserId: string;
  onStartVideoCall?: () => void;
  onSendMessage?: (message: string) => void;
}

// Helper function to transform team presence data
function transformTeamPresenceData(teamPresence: any[]): TeamMember[] {
  return teamPresence.map(member => ({
    id: member.id,
    name: member.user_name || 'Unknown User',
    avatar: null, // TODO: Get from user profile
    isOnline: member.is_online,
    currentView: member.current_view || "3d_model",
    position: member.current_position ? {
      x: member.current_position.x || 0,
      y: member.current_position.y || 5,
      z: member.current_position.z || 0
    } : { x: 0, y: 5, z: 0 },
    lastSeen: member.last_seen,
    role: member.role || 'Team Member',
    deviceInfo: member.device_info || { type: "unknown", browser: "unknown" }
  }));
}

function TeamMemberCursor({ member }: { member: TeamMember }) {
  const cursorRef = useRef<THREE.Group>(null);
  const [isHovered, setIsHovered] = useState(false);

  // Animate cursor movement
  useFrame((state) => {
    if (cursorRef.current && member.position) {
      // Smooth movement to target position
      cursorRef.current.position.lerp(
        new THREE.Vector3(member.position.x, member.position.y, member.position.z),
        0.1
      );
      
      // Add subtle floating animation
      cursorRef.current.position.y += Math.sin(state.clock.elapsedTime * 2) * 0.1;
    }
  });

  if (!member.isOnline || !member.position) return null;

  return (
    <group 
      ref={cursorRef}
      onPointerEnter={() => setIsHovered(true)}
      onPointerLeave={() => setIsHovered(false)}
    >
      {/* 3D cursor indicator */}
      <mesh position={[0, 2, 0]}>
        <coneGeometry args={[0.3, 1, 8]} />
        <meshBasicMaterial color="#3B82F6" transparent opacity={0.8} />
      </mesh>
      
      {/* User info popup */}
      <Html position={[0, 3, 0]} center>
        <div className={`transition-all duration-200 ${isHovered ? 'scale-110' : 'scale-100'}`}>
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-2 flex items-center space-x-2 min-w-max">
            <Avatar className="h-6 w-6">
              <AvatarImage src={member.avatar} />
              <AvatarFallback>{member.name.split(' ').map(n => n[0]).join('')}</AvatarFallback>
            </Avatar>
            <div className="text-xs">
              <div className="font-medium">{member.name}</div>
              <div className="text-muted-foreground">{member.role}</div>
            </div>
            <div className="flex items-center space-x-1">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <Eye className="h-3 w-3 text-blue-500" />
            </div>
          </div>
        </div>
      </Html>
    </group>
  );
}

export function ConstructionTeamPresence({
  projectId,
  currentUserId,
  onStartVideoCall,
  onSendMessage
}: Props) {
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([]);
  const [showPresencePanel, setShowPresencePanel] = useState(false);
  const [isVideoCallActive, setIsVideoCallActive] = useState(false);
  const [isMuted, setIsMuted] = useState(false);

  const { camera } = useThree();

  // TODO: Replace with real tRPC query for team presence
  // const { data: teamPresenceData } = useTRPC.teamPresence?.getByProject?.useQuery({
  //   projectId,
  // });

  // TODO: Set up real-time WebSocket connection for team presence updates
  useEffect(() => {
    // This would typically connect to a WebSocket or Supabase realtime subscription
    // to get live team presence updates
  }, [projectId]);

  // Update current user position
  useFrame(() => {
    const currentUser = teamMembers.find(m => m.id === currentUserId);
    if (currentUser) {
      // Update position based on camera
      const newPosition = {
        x: camera.position.x,
        y: camera.position.y,
        z: camera.position.z
      };
      
      // In real app, this would send to WebSocket
      // updateUserPosition(currentUserId, newPosition);
    }
  });

  const onlineMembers = teamMembers.filter(m => m.isOnline && m.id !== currentUserId);
  const offlineMembers = teamMembers.filter(m => !m.isOnline);

  const formatLastSeen = (lastSeen: string) => {
    const diff = Date.now() - new Date(lastSeen).getTime();
    const minutes = Math.floor(diff / 60000);
    if (minutes < 1) return "Just now";
    if (minutes < 60) return `${minutes}m ago`;
    const hours = Math.floor(minutes / 60);
    if (hours < 24) return `${hours}h ago`;
    return `${Math.floor(hours / 24)}d ago`;
  };

  return (
    <>
      {/* Render team member cursors in 3D space */}
      {onlineMembers.map(member => (
        <TeamMemberCursor key={member.id} member={member} />
      ))}
      
      {/* Team presence UI overlay */}
      <Html fullscreen>
        {/* Floating team indicator */}
        <div className="absolute top-4 right-4 z-50">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowPresencePanel(!showPresencePanel)}
            className="bg-white dark:bg-gray-800 shadow-lg"
          >
            <Users className="h-4 w-4 mr-1" />
            {onlineMembers.length + 1}
            <div className="ml-1 w-2 h-2 bg-green-500 rounded-full"></div>
          </Button>
        </div>

        {/* Team presence panel */}
        {showPresencePanel && (
          <div className="absolute top-16 right-4 z-50">
            <Card className="w-80 shadow-xl">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm flex items-center justify-between">
                  <span>Team Presence</span>
                  <div className="flex items-center space-x-1">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setIsVideoCallActive(!isVideoCallActive)}
                      className={`h-7 ${isVideoCallActive ? 'bg-green-100 text-green-700' : ''}`}
                    >
                      <Video className="h-3 w-3" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setIsMuted(!isMuted)}
                      className={`h-7 ${isMuted ? 'bg-red-100 text-red-700' : ''}`}
                    >
                      {isMuted ? <MicOff className="h-3 w-3" /> : <Mic className="h-3 w-3" />}
                    </Button>
                  </div>
                </CardTitle>
              </CardHeader>
              
              <CardContent className="space-y-3">
                {/* Online members */}
                <div>
                  <h4 className="text-xs font-medium text-muted-foreground mb-2">
                    Online ({onlineMembers.length + 1})
                  </h4>
                  
                  {/* Current user */}
                  <div className="flex items-center space-x-2 p-2 rounded-lg bg-blue-50 dark:bg-blue-900/20">
                    <Avatar className="h-6 w-6">
                      <AvatarFallback>You</AvatarFallback>
                    </Avatar>
                    <div className="flex-1 min-w-0">
                      <div className="text-xs font-medium">You</div>
                      <div className="text-xs text-muted-foreground">Viewing 3D model</div>
                    </div>
                    <Badge variant="secondary" className="text-xs">Host</Badge>
                  </div>
                  
                  {/* Other online members */}
                  {onlineMembers.map(member => (
                    <div key={member.id} className="flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800">
                      <Avatar className="h-6 w-6">
                        <AvatarImage src={member.avatar} />
                        <AvatarFallback>{member.name.split(' ').map(n => n[0]).join('')}</AvatarFallback>
                      </Avatar>
                      <div className="flex-1 min-w-0">
                        <div className="text-xs font-medium">{member.name}</div>
                        <div className="text-xs text-muted-foreground">{member.role}</div>
                      </div>
                      <div className="flex items-center space-x-1">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                          <MessageCircle className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
                
                {/* Offline members */}
                {offlineMembers.length > 0 && (
                  <div>
                    <h4 className="text-xs font-medium text-muted-foreground mb-2">
                      Offline ({offlineMembers.length})
                    </h4>
                    {offlineMembers.map(member => (
                      <div key={member.id} className="flex items-center space-x-2 p-2 rounded-lg opacity-60">
                        <Avatar className="h-6 w-6">
                          <AvatarImage src={member.avatar} />
                          <AvatarFallback>{member.name.split(' ').map(n => n[0]).join('')}</AvatarFallback>
                        </Avatar>
                        <div className="flex-1 min-w-0">
                          <div className="text-xs font-medium">{member.name}</div>
                          <div className="text-xs text-muted-foreground">
                            Last seen {formatLastSeen(member.lastSeen)}
                          </div>
                        </div>
                        <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        )}
      </Html>
    </>
  );
}
