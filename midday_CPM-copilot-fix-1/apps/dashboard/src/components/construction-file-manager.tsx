"use client";

import { FileUpload, type UploadedFile } from "@/components/file-upload";
import { useTRPC } from "@/trpc/client";
import { Badge } from "@midday/ui/badge";
import { Button } from "@midday/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@midday/ui/card";
import { Input } from "@midday/ui/input";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@midday/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@midday/ui/tabs";
import { formatDistanceToNow } from "date-fns";
import { 
  Download, 
  Eye, 
  FileText, 
  Folder, 
  Image, 
  Search,
  Upload as UploadIcon
} from "lucide-react";
import { useState } from "react";

interface ProjectFile extends UploadedFile {
  category: 'cad' | 'bim' | 'photo' | 'document' | 'other';
  uploadedBy?: string;
  uploadedAt?: string;
  description?: string;
  phase?: string;
}

interface ConstructionFileManagerProps {
  projectId: string;
}

const fileCategories = {
  cad: { label: "CAD Files", icon: "📐", color: "bg-blue-100 text-blue-800" },
  bim: { label: "BIM Models", icon: "🏗️", color: "bg-green-100 text-green-800" },
  photo: { label: "Photos", icon: "📷", color: "bg-purple-100 text-purple-800" },
  document: { label: "Documents", icon: "📄", color: "bg-orange-100 text-orange-800" },
  other: { label: "Other", icon: "📁", color: "bg-gray-100 text-gray-800" },
};

const phases = [
  "site_preparation",
  "foundation", 
  "framing",
  "roofing",
  "electrical",
  "plumbing",
  "insulation",
  "drywall",
  "flooring",
  "painting",
  "final_inspection"
];

export function ConstructionFileManager({ projectId }: ConstructionFileManagerProps) {
  const [files, setFiles] = useState<ProjectFile[]>([]);

  // TODO: Replace with real tRPC query for construction files
  // const { data: constructionFiles } = useTRPC.constructionFiles?.getByProject?.useQuery({
  //   projectId,
  // });

  // useEffect(() => {
  //   if (constructionFiles) {
  //     setFiles(constructionFiles.map(file => ({
  //       id: file.id,
  //       name: file.name,
  //       size: file.size,
  //       type: file.mimeType,
  //       url: file.url,
  //       category: determineFileCategory(file.mimeType, file.name),
  //       uploadedBy: file.uploadedBy,
  //       uploadedAt: file.createdAt,
  //       description: file.description,
  //       phase: file.phase,
  //     })));
  //   }
  // }, [constructionFiles]);
  
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string>("all");
  const [selectedPhase, setSelectedPhase] = useState<string>("all");

  const api = useTRPC();

  const handleFileUpload = async (newFiles: File[]): Promise<UploadedFile[]> => {
    // TODO: Implement real file upload using tRPC mutation
    // const uploadMutation = api.constructionFiles.upload.useMutation();

    try {
      // For now, return empty array until real upload is implemented
      console.log('Files to upload:', newFiles.map(f => f.name));
      return [];

      // Real implementation would be:
      // const uploadedFiles = await Promise.all(
      //   newFiles.map(async (file) => {
      //     const result = await uploadMutation.mutateAsync({
      //       projectId,
      //       file,
      //       description: '',
      //       phase: 'foundation', // or get from form
      //     });
      //     return result;
      //   })
      // );
      // return uploadedFiles;
    } catch (error) {
      console.error('File upload failed:', error);
      throw error;
    }
  };

  const determineFileCategory = (type: string, name: string): ProjectFile['category'] => {
    const ext = name.split('.').pop()?.toLowerCase();
    
    if (type.startsWith('image/')) return 'photo';
    if (ext && ['dwg', 'dxf', 'step', 'stp', 'iges'].includes(ext)) return 'cad';
    if (ext && ['ifc', 'rvt', 'rfa', 'nwd', 'nwc'].includes(ext)) return 'bim';
    if (ext && ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'txt'].includes(ext)) return 'document';
    
    return 'other';
  };

  const filteredFiles = files.filter(file => {
    const matchesSearch = file.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         file.description?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === "all" || file.category === selectedCategory;
    const matchesPhase = selectedPhase === "all" || file.phase === selectedPhase;
    
    return matchesSearch && matchesCategory && matchesPhase;
  });

  const filesByCategory = Object.entries(fileCategories).map(([key, category]) => ({
    key,
    ...category,
    count: files.filter(f => f.category === key).length,
    files: filteredFiles.filter(f => f.category === key),
  }));

  return (
    <div className="space-y-6">
      {/* Upload Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <UploadIcon className="h-5 w-5" />
            Upload Project Files
          </CardTitle>
        </CardHeader>
        <CardContent>
          <FileUpload
            files={files}
            onFilesChange={(newFiles) => setFiles(newFiles as ProjectFile[])}
            onUpload={handleFileUpload}
            maxFiles={50}
            description="Upload CAD files, BIM models, photos, documents, and other project files"
            accept={{
              "image/*": [".png", ".jpg", ".jpeg", ".gif", ".webp"],
              "application/pdf": [".pdf"],
              "application/acad": [".dwg", ".dxf"],
              "model/step": [".step", ".stp"],
              "model/iges": [".iges", ".igs"],
              "application/ifc": [".ifc"],
              "application/vnd.autodesk.revit": [".rvt", ".rfa"],
              "application/msword": [".doc"],
              "application/vnd.openxmlformats-officedocument.wordprocessingml.document": [".docx"],
              "application/vnd.ms-excel": [".xls"],
              "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": [".xlsx"],
              "text/plain": [".txt"],
            }}
          />
        </CardContent>
      </Card>

      {/* Filters */}
      <div className="flex items-center gap-4 flex-wrap">
        <div className="relative flex-1 min-w-[200px] max-w-sm">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder="Search files..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        
        <Select value={selectedCategory} onValueChange={setSelectedCategory}>
          <SelectTrigger className="w-48">
            <SelectValue placeholder="Filter by category" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Categories</SelectItem>
            {Object.entries(fileCategories).map(([key, category]) => (
              <SelectItem key={key} value={key}>
                {category.icon} {category.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select value={selectedPhase} onValueChange={setSelectedPhase}>
          <SelectTrigger className="w-48">
            <SelectValue placeholder="Filter by phase" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Phases</SelectItem>
            {phases.map((phase) => (
              <SelectItem key={phase} value={phase}>
                {phase.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* File Browser */}
      <Tabs defaultValue="grid" className="space-y-4">
        <TabsList>
          <TabsTrigger value="grid">Grid View</TabsTrigger>
          <TabsTrigger value="list">List View</TabsTrigger>
          <TabsTrigger value="category">By Category</TabsTrigger>
        </TabsList>

        <TabsContent value="grid" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {filteredFiles.map((file) => (
              <Card key={file.id} className="group hover:shadow-md transition-shadow">
                <CardContent className="p-4">
                  <div className="flex items-start justify-between mb-2">
                    <div className="text-2xl">
                      {fileCategories[file.category].icon}
                    </div>
                    <Badge 
                      variant="secondary"
                      className={fileCategories[file.category].color}
                    >
                      {fileCategories[file.category].label}
                    </Badge>
                  </div>
                  
                  <h3 className="font-medium text-sm truncate mb-1" title={file.name}>
                    {file.name}
                  </h3>
                  
                  {file.description && (
                    <p className="text-xs text-muted-foreground mb-2 line-clamp-2">
                      {file.description}
                    </p>
                  )}
                  
                  <div className="text-xs text-muted-foreground space-y-1">
                    <p>{(file.size / 1024 / 1024).toFixed(1)} MB</p>
                    {file.uploadedAt && (
                      <p>
                        {formatDistanceToNow(new Date(file.uploadedAt))} ago
                        {file.uploadedBy && ` by ${file.uploadedBy}`}
                      </p>
                    )}
                    {file.phase && (
                      <p className="capitalize">{file.phase.replace('_', ' ')}</p>
                    )}
                  </div>
                  
                  <div className="flex items-center gap-1 mt-3 opacity-0 group-hover:opacity-100 transition-opacity">
                    <Button variant="ghost" size="sm" className="h-7 px-2">
                      <Eye className="h-3 w-3" />
                    </Button>
                    <Button variant="ghost" size="sm" className="h-7 px-2">
                      <Download className="h-3 w-3" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="list" className="space-y-2">
          {filteredFiles.map((file) => (
            <Card key={file.id}>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3 flex-1 min-w-0">
                    <div className="text-xl">{fileCategories[file.category].icon}</div>
                    <div className="flex-1 min-w-0">
                      <h3 className="font-medium truncate">{file.name}</h3>
                      {file.description && (
                        <p className="text-sm text-muted-foreground truncate">
                          {file.description}
                        </p>
                      )}
                    </div>
                    <Badge 
                      variant="secondary"
                      className={fileCategories[file.category].color}
                    >
                      {fileCategories[file.category].label}
                    </Badge>
                  </div>
                  
                  <div className="flex items-center gap-4 text-sm text-muted-foreground">
                    <span>{(file.size / 1024 / 1024).toFixed(1)} MB</span>
                    {file.uploadedAt && (
                      <span>
                        {formatDistanceToNow(new Date(file.uploadedAt))} ago
                      </span>
                    )}
                    <div className="flex items-center gap-1">
                      <Button variant="ghost" size="sm" className="h-7 px-2">
                        <Eye className="h-3 w-3" />
                      </Button>
                      <Button variant="ghost" size="sm" className="h-7 px-2">
                        <Download className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </TabsContent>

        <TabsContent value="category" className="space-y-6">
          {filesByCategory.map((category) => (
            <div key={category.key}>
              <div className="flex items-center gap-2 mb-4">
                <span className="text-xl">{category.icon}</span>
                <h3 className="text-lg font-semibold">{category.label}</h3>
                <Badge variant="secondary">{category.count}</Badge>
              </div>
              
              {category.files.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {category.files.map((file) => (
                    <Card key={file.id}>
                      <CardContent className="p-4">
                        <h4 className="font-medium text-sm truncate mb-1" title={file.name}>
                          {file.name}
                        </h4>
                        {file.description && (
                          <p className="text-xs text-muted-foreground mb-2">
                            {file.description}
                          </p>
                        )}
                        <div className="text-xs text-muted-foreground">
                          <p>{(file.size / 1024 / 1024).toFixed(1)} MB</p>
                          {file.uploadedAt && (
                            <p>{formatDistanceToNow(new Date(file.uploadedAt))} ago</p>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : (
                <p className="text-muted-foreground text-sm">
                  No {category.label.toLowerCase()} uploaded yet
                </p>
              )}
            </div>
          ))}
        </TabsContent>
      </Tabs>
    </div>
  );
}