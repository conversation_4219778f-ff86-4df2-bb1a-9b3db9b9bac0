"use client";

import { useTR<PERSON> } from "@/trpc/client";
import { useQuery } from "@tanstack/react-query";
import { BankAccount } from "./bank-account";

export function ManualAccounts() {
  const trpc = useTRPC();

  // Use regular useQuery instead of useSuspenseQuery to handle errors gracefully
  const { data, error, isLoading } = useQuery({
    ...trpc.bankAccounts.get.queryOptions({
      manual: true,
    }),
    retry: false,
    refetchOnWindowFocus: false,
  });

  // If there's a database error or loading, show fallback
  if (error || isLoading || !data) {
    return (
      <div className="px-6 py-8 text-center">
        <div className="text-sm text-muted-foreground">
          {error ? "Manual accounts unavailable" : "Loading accounts..."}
        </div>
        {error && (
          <div className="text-xs text-muted-foreground mt-1">
            Database setup in progress
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="px-6 pb-6 space-y-6 divide-y">
      {data?.map((account) => (
        <BankAccount key={account.id} data={account} />
      ))}
    </div>
  );
}
