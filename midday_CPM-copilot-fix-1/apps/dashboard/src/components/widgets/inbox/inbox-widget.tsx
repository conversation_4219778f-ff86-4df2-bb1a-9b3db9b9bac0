"use client";

import { CopyInput } from "@/components/copy-input";
import { useUserQuery } from "@/hooks/use-user";
import { useTRPC } from "@/trpc/client";
import { getInboxEmail } from "@midday/inbox";
import { useQuery } from "@tanstack/react-query";
import { InboxList } from "./inbox-list";

export function InboxWidget() {
  const trpc = useTRPC();
  const { data: user } = useUserQuery();

  // Use regular useQuery instead of useSuspenseQuery to handle errors gracefully
  const { data, error, isLoading } = useQuery({
    ...trpc.inbox.get.queryOptions(),
    retry: false,
    refetchOnWindowFocus: false,
  });

  // If there's a database error, show a fallback state
  if (error) {
    return (
      <div className="flex flex-col space-y-4 items-center justify-center h-full text-center">
        <p className="text-sm text-[#606060]">
          Inbox data unavailable
          <br />
          Database setup in progress
        </p>
      </div>
    );
  }

  // Show loading state
  if (isLoading) {
    return (
      <div className="flex flex-col space-y-4 items-center justify-center h-full text-center">
        <p className="text-sm text-[#606060]">Loading inbox...</p>
      </div>
    );
  }

  if (!data?.data?.length) {
    return (
      <div className="flex flex-col space-y-4 items-center justify-center h-full text-center">
        <div>
          <CopyInput value={getInboxEmail(user?.team?.inboxId ?? "")} />
        </div>

        <p className="text-sm text-[#606060]">
          Use this email for online purchases to seamlessly
          <br />
          match invoices againsts transactions.
        </p>
      </div>
    );
  }

  return <InboxList data={data.data} />;
}
