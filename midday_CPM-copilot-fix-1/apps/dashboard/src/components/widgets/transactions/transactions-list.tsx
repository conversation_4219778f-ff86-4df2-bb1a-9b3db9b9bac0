"use client";

import { useTR<PERSON> } from "@/trpc/client";
import { useQuery } from "@tanstack/react-query";
import type { TransactionType } from "./data";
import { TransactionsItemList } from "./transactions-item-list";

type Props = {
  type: TransactionType;
  disabled: boolean;
};

export function TransactionsList({ type, disabled }: Props) {
  const trpc = useTRPC();

  // Use regular useQuery instead of useSuspenseQuery to handle errors gracefully
  const { data: transactions, error, isLoading } = useQuery({
    ...trpc.transactions.get.queryOptions({
      pageSize: 15,
      type: type === "all" ? undefined : type,
    }),
    retry: false,
    refetchOnWindowFocus: false,
  });

  // If there's a database error, show a fallback state
  if (error) {
    return (
      <div className="flex items-center justify-center aspect-square">
        <div className="text-center">
          <p className="text-sm text-[#606060] -mt-12">Transactions unavailable</p>
          <p className="text-xs text-muted-foreground mt-1">Database setup in progress</p>
        </div>
      </div>
    );
  }

  // Show loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center aspect-square">
        <p className="text-sm text-[#606060] -mt-12">Loading transactions...</p>
      </div>
    );
  }

  if (!transactions?.data?.length) {
    return (
      <div className="flex items-center justify-center aspect-square">
        <p className="text-sm text-[#606060] -mt-12">No transactions found</p>
      </div>
    );
  }

  return (
    <TransactionsItemList
      transactions={transactions?.data}
      disabled={disabled}
    />
  );
}
