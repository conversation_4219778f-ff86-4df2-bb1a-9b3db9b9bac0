"use client";

import { PaymentScoreVisualizer } from "@/components/payment-score-visualizer";
import { useI18n } from "@/locales/client";
import { useTRPC } from "@/trpc/client";
import { useQuery } from "@tanstack/react-query";
import { Invoice } from "./invoice";

export function InvoiceWidget() {
  const trpc = useTRPC();
  const t = useI18n();

  // Use regular useQuery instead of useSuspenseQuery to handle errors gracefully
  const { data: invoices, error: invoicesError } = useQuery({
    ...trpc.invoice.get.queryOptions({ pageSize: 10 }),
    retry: false,
    refetchOnWindowFocus: false,
  });

  const { data: paymentStatus, error: paymentError } = useQuery({
    ...trpc.invoice.paymentStatus.queryOptions(),
    retry: false,
    refetchOnWindowFocus: false,
  });

  // If there are database errors, show a fallback state
  if (invoicesError || paymentError) {
    return (
      <div className="mt-8">
        <div className="flex justify-center items-center p-8 text-muted-foreground">
          <div className="text-center">
            <div className="text-sm">Invoice data unavailable</div>
            <div className="text-xs mt-1">Database setup in progress</div>
          </div>
        </div>
      </div>
    );
  }

  // Show loading state while data is being fetched
  if (!invoices || !paymentStatus) {
    return (
      <div className="mt-8">
        <div className="flex justify-center items-center p-8">
          <div className="text-sm text-muted-foreground">Loading invoices...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="mt-8">
      <div className="flex justify-between items-center p-3 py-2 border border-border">
        <div>
          <div className="flex flex-col gap-2">
            {/* @ts-expect-error */}
            <div>{t(`payment_status.${paymentStatus?.paymentStatus}`)}</div>
            <div className="text-sm text-muted-foreground">Payment score</div>
          </div>
        </div>

        <PaymentScoreVisualizer
          score={paymentStatus?.score ?? 0}
          paymentStatus={paymentStatus?.paymentStatus ?? "none"}
        />
      </div>

      <Invoice invoices={invoices?.data ?? []} />
    </div>
  );
}
