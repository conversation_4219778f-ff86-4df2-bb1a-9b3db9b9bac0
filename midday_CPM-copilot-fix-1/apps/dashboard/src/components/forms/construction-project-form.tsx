"use client";

import { SearchCustomers } from "@/components/search-customers";
import { useZodForm } from "@/hooks/use-zod-form";
import { useTRPC } from "@/trpc/client";
import type { RouterOutputs } from "@api/trpc/routers/_app";
import { uniqueCurrencies } from "@midday/location/currencies";
import { Collapsible, CollapsibleContent } from "@midday/ui/collapsible";
import { CurrencyInput } from "@midday/ui/currency-input";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@midday/ui/form";
import { Input } from "@midday/ui/input";
import { Label } from "@midday/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@midday/ui/select";
import { SubmitButton } from "@midday/ui/submit-button";
import { Textarea } from "@midday/ui/textarea";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { z } from "zod";

const formSchema = z.object({
  id: z.string().uuid().optional(),
  name: z.string().min(1),
  description: z.string().optional(),
  location: z.string().optional(),
  address: z.string().optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  estimatedCost: z.number().optional(),
  actualCost: z.number().optional(),
  currency: z.string().optional(),
  completionPercentage: z.number().min(0).max(100).optional().default(0),
  siteArea: z.number().optional(),
  buildingArea: z.number().optional(),
  currentPhase: z
    .enum([
      "site_preparation",
      "foundation",
      "framing",
      "roofing",
      "electrical",
      "plumbing",
      "insulation",
      "drywall",
      "flooring",
      "painting",
      "final_inspection",
    ])
    .optional(),
  status: z
    .enum(["planning", "in_progress", "on_hold", "completed", "cancelled"])
    .optional(),
  customerId: z.string().uuid().nullable().optional(),
  contractorInfo: z
    .object({
      name: z.string().optional(),
      contact: z.string().optional(),
      license: z.string().optional(),
      insurance: z.string().optional(),
    })
    .optional(),
  permitInfo: z
    .object({
      number: z.string().optional(),
      issued: z.string().optional(),
      expires: z.string().optional(),
      type: z.string().optional(),
    })
    .optional(),
  siteCoordinates: z
    .object({
      latitude: z.number().optional(),
      longitude: z.number().optional(),
      elevation: z.number().optional(),
    })
    .optional(),
});

type FormValues = z.infer<typeof formSchema>;
type Project = RouterOutputs["constructionProjects"]["getById"];

interface Props {
  project?: Project;
  onSuccess?: () => void;
}

const phaseLabels = {
  site_preparation: "Site Preparation",
  foundation: "Foundation",
  framing: "Framing",
  roofing: "Roofing",
  electrical: "Electrical",
  plumbing: "Plumbing",
  insulation: "Insulation",
  drywall: "Drywall",
  flooring: "Flooring",
  painting: "Painting",
  final_inspection: "Final Inspection",
};

const statusLabels = {
  planning: "Planning",
  in_progress: "In Progress",
  on_hold: "On Hold",
  completed: "Completed",
  cancelled: "Cancelled",
};

export function ConstructionProjectForm({ project, onSuccess }: Props) {
  const api = useTRPC();
  const queryClient = useQueryClient();

  const form = useZodForm(formSchema, {
    defaultValues: {
      id: project?.id,
      name: project?.name || "",
      description: project?.description || "",
      location: project?.location || "",
      address: project?.address || "",
      startDate: project?.startDate || "",
      endDate: project?.endDate || "",
      estimatedCost: project?.estimatedCost || undefined,
      actualCost: project?.actualCost || undefined,
      currency: project?.currency || "USD",
      completionPercentage: project?.completionPercentage || 0,
      siteArea: project?.siteArea || undefined,
      buildingArea: project?.buildingArea || undefined,
      currentPhase: project?.currentPhase || undefined,
      status: project?.status || "planning",
      customerId: project?.customer?.id || null,
      contractorInfo: project?.contractorInfo || {},
      permitInfo: project?.permitInfo || {},
      siteCoordinates: project?.siteCoordinates || {},
    },
  });

  // Working mutation using direct API calls
  const mutation = useMutation({
    mutationFn: async (values: FormValues) => {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/trpc/constructionProjects.upsert`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(values),
      });

      if (!response.ok) {
        throw new Error(`Failed to ${project ? 'update' : 'create'} construction project`);
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["constructionProjects"],
      });
      onSuccess?.();
    },
    onError: (error) => {
      console.error("Error creating/updating construction project:", error);
    },
  });

  const onSubmit = (values: FormValues) => {
    mutation.mutate(values);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        {/* Basic Information */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Basic Information</h3>
          
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Project Name</FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    placeholder="e.g., Downtown Office Building"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="description"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Description</FormLabel>
                <FormControl>
                  <Textarea
                    {...field}
                    placeholder="Detailed description of the construction project"
                    rows={3}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="grid grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="status"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Status</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select status" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {Object.entries(statusLabels).map(([value, label]) => (
                        <SelectItem key={value} value={value}>
                          {label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="currentPhase"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Current Phase</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select phase" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {Object.entries(phaseLabels).map(([value, label]) => (
                        <SelectItem key={value} value={value}>
                          {label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <FormField
            control={form.control}
            name="completionPercentage"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Completion Percentage</FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    type="number"
                    min="0"
                    max="100"
                    placeholder="0"
                    onChange={(e) => field.onChange(Number(e.target.value))}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {/* Location Information */}
        <Collapsible>
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Location Information</h3>
            
            <FormField
              control={form.control}
              name="location"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Location</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      placeholder="e.g., Downtown, New York"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="address"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Address</FormLabel>
                  <FormControl>
                    <Textarea
                      {...field}
                      placeholder="Full project address"
                      rows={2}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <CollapsibleContent>
              <div className="grid grid-cols-3 gap-4">
                <FormField
                  control={form.control}
                  name="siteCoordinates.latitude"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Latitude</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          type="number"
                          step="any"
                          placeholder="40.7589"
                          onChange={(e) => field.onChange(Number(e.target.value))}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="siteCoordinates.longitude"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Longitude</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          type="number"
                          step="any"
                          placeholder="-73.9851"
                          onChange={(e) => field.onChange(Number(e.target.value))}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="siteCoordinates.elevation"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Elevation (ft)</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          type="number"
                          step="any"
                          placeholder="15.2"
                          onChange={(e) => field.onChange(Number(e.target.value))}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </CollapsibleContent>
          </div>
        </Collapsible>

        {/* Financial Information */}
        <Collapsible>
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Financial Information</h3>
            
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="startDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Start Date</FormLabel>
                    <FormControl>
                      <Input {...field} type="date" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="endDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>End Date</FormLabel>
                    <FormControl>
                      <Input {...field} type="date" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <CollapsibleContent>
              <div className="space-y-4">
                <FormField
                  control={form.control}
                  name="currency"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Currency</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select currency" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {uniqueCurrencies.map((currency) => (
                            <SelectItem key={currency.value} value={currency.value}>
                              {currency.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="estimatedCost"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Estimated Cost</FormLabel>
                        <FormControl>
                          <CurrencyInput
                            {...field}
                            placeholder="2500000"
                            currency={form.watch("currency") || "USD"}
                            onValueChange={(values) => {
                              field.onChange(values.floatValue || 0);
                            }}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="actualCost"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Actual Cost</FormLabel>
                        <FormControl>
                          <CurrencyInput
                            {...field}
                            placeholder="500000"
                            currency={form.watch("currency") || "USD"}
                            onValueChange={(values) => {
                              field.onChange(values.floatValue || 0);
                            }}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="siteArea"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Site Area (sq ft)</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            type="number"
                            placeholder="50000"
                            onChange={(e) => field.onChange(Number(e.target.value))}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="buildingArea"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Building Area (sq ft)</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            type="number"
                            placeholder="180000"
                            onChange={(e) => field.onChange(Number(e.target.value))}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            </CollapsibleContent>
          </div>
        </Collapsible>

        {/* Customer */}
        <FormField
          control={form.control}
          name="customerId"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Customer</FormLabel>
              <FormControl>
                <SearchCustomers
                  onSelect={(customer) => field.onChange(customer?.id)}
                  selectedId={field.value}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Additional Information */}
        <Collapsible>
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Contractor Information</h3>
            
            <CollapsibleContent>
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="contractorInfo.name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Contractor Name</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="ABC Construction" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="contractorInfo.contact"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Contact</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="<EMAIL>" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="contractorInfo.license"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>License Number</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="LIC123456" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="contractorInfo.insurance"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Insurance Policy</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="INS789012" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </CollapsibleContent>
          </div>
        </Collapsible>

        <Collapsible>
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Permit Information</h3>
            
            <CollapsibleContent>
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="permitInfo.number"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Permit Number</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="PERMIT-2024-001" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="permitInfo.type"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Permit Type</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="Commercial Construction" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="permitInfo.issued"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Issued Date</FormLabel>
                      <FormControl>
                        <Input {...field} type="date" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="permitInfo.expires"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Expires Date</FormLabel>
                      <FormControl>
                        <Input {...field} type="date" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </CollapsibleContent>
          </div>
        </Collapsible>

        <SubmitButton
          isSubmitting={mutation.isPending}
          disabled={!form.formState.isValid || mutation.isPending}
          className="w-full"
        >
          {project ? "Update Project" : "Create Project"}
        </SubmitButton>

        {/* Error Display */}
        {mutation.error && (
          <div className="mt-4 p-3 rounded-md bg-destructive/10 border border-destructive/20">
            <p className="text-sm text-destructive">
              {mutation.error.message || "An error occurred while saving the project"}
            </p>
          </div>
        )}
      </form>
    </Form>
  );
}