"use client";

import { useZodForm } from "@/hooks/use-zod-form";
import { useTRPC } from "@/trpc/client";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@midday/ui/form";
import { Input } from "@midday/ui/input";
import { SubmitButton } from "@midday/ui/submit-button";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { z } from "zod";

const testFormSchema = z.object({
  name: z.string().min(1, "Project name is required"),
  description: z.string().optional(),
});

type TestFormValues = z.infer<typeof testFormSchema>;

interface Props {
  onSuccess?: () => void;
}

export function TestConstructionForm({ onSuccess }: Props) {
  const api = useTRPC();
  const queryClient = useQueryClient();

  const form = useZodForm(testFormSchema, {
    defaultValues: {
      name: "",
      description: "",
    },
  });

  const mutation = useMutation({
    mutationFn: (values: TestFormValues) => {
      console.log("Form values:", values);
      // Test the API call
      return api.constructionProjects.upsert.mutate({
        name: values.name,
        description: values.description,
      });
    },
    onSuccess: () => {
      console.log("Project created successfully");
      queryClient.invalidateQueries({
        queryKey: ["constructionProjects"],
      });
      onSuccess?.();
    },
    onError: (error) => {
      console.error("Error creating project:", error);
    },
  });

  const onSubmit = (values: TestFormValues) => {
    console.log("Submitting form:", values);
    mutation.mutate(values);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Project Name</FormLabel>
              <FormControl>
                <Input
                  {...field}
                  placeholder="Enter project name"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Description (Optional)</FormLabel>
              <FormControl>
                <Input
                  {...field}
                  placeholder="Enter project description"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <SubmitButton
          isSubmitting={mutation.isPending}
          disabled={!form.formState.isValid || mutation.isPending}
          className="w-full"
        >
          Create Test Project
        </SubmitButton>
      </form>
    </Form>
  );
}
