"use client";

import { Badge } from "@midday/ui/badge";
import { <PERSON>, <PERSON>Content, Card<PERSON><PERSON>er, CardTitle } from "@midday/ui/card";
import { Progress } from "@midday/ui/progress";
import { formatDistanceToNow, isValid, format } from "date-fns";
import { Building2, Calendar, DollarSign, MapPin, Users } from "lucide-react";
import { useState, useEffect } from "react";
import type { RouterOutputs } from "@api/trpc/routers/_app";

type ConstructionProject = RouterOutputs["constructionProjects"]["get"]["data"][0];

interface Props {
  project: ConstructionProject;
  onClick?: () => void;
}

const statusColors = {
  planning: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",
  in_progress: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
  on_hold: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300",
  completed: "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300",
  cancelled: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300",
};

const phaseLabels = {
  site_preparation: "Site Preparation",
  foundation: "Foundation",
  framing: "Framing",
  roofing: "Roofing",
  electrical: "Electrical",
  plumbing: "Plumbing",
  insulation: "Insulation",
  drywall: "Drywall",
  flooring: "Flooring",
  painting: "Painting",
  final_inspection: "Final Inspection",
};

const statusLabels = {
  planning: "Planning",
  in_progress: "In Progress",
  on_hold: "On Hold",
  completed: "Completed",
  cancelled: "Cancelled",
};

// Client-side only date component to prevent hydration mismatches
function ClientOnlyDate({ date }: { date: string }) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    // Return a stable fallback during SSR that matches the expected structure
    return <span suppressHydrationWarning>Recently updated</span>;
  }

  const updateDate = new Date(date);
  if (!isValid(updateDate)) {
    return <span suppressHydrationWarning>Recently updated</span>;
  }

  return <span suppressHydrationWarning>Updated {formatDistanceToNow(updateDate)} ago</span>;
}

function ClientOnlyStartDate({ date }: { date: string }) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return <div className="text-xs text-muted-foreground" suppressHydrationWarning></div>; // Stable placeholder
  }

  const startDate = new Date(date);
  if (!isValid(startDate)) {
    return <div className="text-xs text-muted-foreground" suppressHydrationWarning></div>;
  }

  return (
    <div className="text-xs text-muted-foreground" suppressHydrationWarning>
      Started {formatDistanceToNow(startDate)} ago
    </div>
  );
}

function ClientOnlyEndDate({ date }: { date: string }) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return <div className="text-xs" suppressHydrationWarning></div>; // Stable placeholder
  }

  const endDate = new Date(date);
  if (!isValid(endDate)) {
    return <div className="text-xs" suppressHydrationWarning></div>;
  }

  return (
    <div className="text-xs" suppressHydrationWarning>
      Due {formatDistanceToNow(endDate)}
    </div>
  );
}

export function ConstructionProjectCard({ project, onClick }: Props) {
  const completion = project.completionPercentage || 0;
  const budgetUsed = project.actualCost && project.estimatedCost 
    ? (project.actualCost / project.estimatedCost) * 100 
    : 0;

  const isOverBudget = budgetUsed > 100;
  const isOnTrack = completion >= 80 && !isOverBudget;

  return (
    <Card 
      className="cursor-pointer transition-all hover:shadow-md" 
      onClick={onClick}
    >
      <CardHeader className="space-y-2">
        <div className="flex items-start justify-between">
          <CardTitle className="text-lg">{project.name}</CardTitle>
          <Badge 
            variant="secondary" 
            className={statusColors[project.status as keyof typeof statusColors]}
          >
            {statusLabels[project.status as keyof typeof statusLabels]}
          </Badge>
        </div>
        
        {project.location && (
          <div className="flex items-center text-sm text-muted-foreground">
            <MapPin className="mr-1 h-3 w-3" />
            {project.location}
          </div>
        )}
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Progress */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span className="text-muted-foreground">Progress</span>
            <span className="font-medium">{completion.toFixed(1)}%</span>
          </div>
          <Progress value={completion} className="h-2" />
        </div>

        {/* Current Phase */}
        {project.currentPhase && (
          <div className="flex items-center justify-between text-sm">
            <span className="text-muted-foreground">Current Phase</span>
            <Badge variant="outline">
              {phaseLabels[project.currentPhase as keyof typeof phaseLabels]}
            </Badge>
          </div>
        )}

        {/* Financial Information */}
        {project.estimatedCost && (
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <div className="flex items-center text-muted-foreground">
                <DollarSign className="mr-1 h-3 w-3" />
                Budget
              </div>
              <div className="text-right">
                <div className="font-medium">
                  {new Intl.NumberFormat('en-US', {
                    style: 'currency',
                    currency: project.currency || 'USD',
                  }).format(project.estimatedCost)}
                </div>
                {project.actualCost && (
                  <div className={`text-xs ${isOverBudget ? 'text-red-600' : 'text-muted-foreground'}`}>
                    {new Intl.NumberFormat('en-US', {
                      style: 'currency',
                      currency: project.currency || 'USD',
                    }).format(project.actualCost)} spent
                  </div>
                )}
              </div>
            </div>
            
            {project.actualCost && (
              <Progress 
                value={Math.min(budgetUsed, 100)} 
                className={`h-1 ${isOverBudget ? '[&>div]:bg-red-500' : ''}`}
              />
            )}
          </div>
        )}

        {/* Timeline */}
        {(project.startDate || project.endDate) && (
          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center text-muted-foreground">
              <Calendar className="mr-1 h-3 w-3" />
              Timeline
            </div>
            <div className="text-right">
              {project.startDate && <ClientOnlyStartDate date={project.startDate} />}
              {project.endDate && <ClientOnlyEndDate date={project.endDate} />}
            </div>
          </div>
        )}

        {/* Project Details */}
        <div className="grid grid-cols-2 gap-4 text-sm">
          {project.siteArea && (
            <div>
              <div className="text-muted-foreground">Site Area</div>
              <div className="font-medium">
                {new Intl.NumberFormat().format(project.siteArea)} sq ft
              </div>
            </div>
          )}
          
          {project.buildingArea && (
            <div>
              <div className="text-muted-foreground">Building Area</div>
              <div className="font-medium">
                {new Intl.NumberFormat().format(project.buildingArea)} sq ft
              </div>
            </div>
          )}
        </div>

        {/* Customer */}
        {project.customer && (
          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center text-muted-foreground">
              <Users className="mr-1 h-3 w-3" />
              Client
            </div>
            <div className="font-medium">{project.customer.name}</div>
          </div>
        )}

        {/* Description */}
        {project.description && (
          <div className="text-sm text-muted-foreground line-clamp-2">
            {project.description}
          </div>
        )}

        {/* Status indicators */}
        <div className="flex items-center justify-between pt-2 border-t">
          <div className="flex items-center space-x-2">
            {isOnTrack && (
              <Badge variant="secondary" className="text-xs bg-green-100 text-green-800">
                On Track
              </Badge>
            )}
            {isOverBudget && (
              <Badge variant="secondary" className="text-xs bg-red-100 text-red-800">
                Over Budget
              </Badge>
            )}
          </div>
          
          <div className="text-xs text-muted-foreground">
            <ClientOnlyDate date={project.updated_at || project.created_at} />
          </div>
        </div>
      </CardContent>
    </Card>
  );
}