import {
  QueryClient,
  defaultShouldDehydrateQuery,
} from "@tanstack/react-query";
import superjson from "superjson";

export function makeQueryClient() {
  return new QueryClient({
    defaultOptions: {
      queries: {
        staleTime: 60 * 1000,
        retry: (failureCount, error) => {
          // Don't retry database connection errors
          if (error?.message?.includes('Failed query:') || error?.message?.includes('database')) {
            return false;
          }
          // Retry other errors up to 3 times
          return failureCount < 3;
        },
        retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
      },
      dehydrate: {
        serializeData: superjson.serialize,
        shouldDehydrateQuery: (query) => {
          // Don't dehydrate failed queries to prevent hydration errors
          if (query.state.status === "error") {
            return false;
          }
          return defaultShouldDehydrateQuery(query) ||
            query.state.status === "pending";
        },
      },
      hydrate: {
        deserializeData: superjson.deserialize,
      },
    },
  });
}
